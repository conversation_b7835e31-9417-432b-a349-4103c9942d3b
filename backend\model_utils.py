# backend/model_utils.py
import os
from google import genai

# Set your Gemini API key here or use environment variable
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
if not GEMINI_API_KEY:
    raise ValueError("GEMINI_API_KEY environment variable is required!")
client = genai.Client(api_key=GEMINI_API_KEY)

def generate_soap_note(transcript: str) -> str:
    prompt = f"""
You are a clinical assistant. Based on the following doctor-patient conversation, extract a SOAP note in this format:

S – Subjective:
O – Objective:
A – Assessment:
P – Plan:

Conversation:
{transcript}
"""
    response = client.models.generate_content(
        model="gemini-2.5-flash",
        contents=[prompt],
        config={"temperature": 0.3}
    )
    return response.text.strip()
