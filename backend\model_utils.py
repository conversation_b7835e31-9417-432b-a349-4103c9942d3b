# backend/model_utils.py
import os
from dotenv import load_dotenv
from groq import Groq

load_dotenv()
GROQ_API_KEY = os.getenv("GROQ_API_KEY")
client = Groq(api_key=GROQ_API_KEY)

def generate_soap_note(transcript: str) -> str:
    prompt = f"""
You are a clinical assistant. Based on the following doctor-patient conversation, extract a SOAP note in this format:

S – Subjective:
O – Objective:
A – Assessment:
P – Plan:

Conversation:
{transcript}
"""
    response = client.chat.completions.create(
        model="mixtral-8x7b-32768",
        messages=[{"role": "user", "content": prompt}],
        temperature=0.3,
    )
    return response.choices[0].message.content.strip()
