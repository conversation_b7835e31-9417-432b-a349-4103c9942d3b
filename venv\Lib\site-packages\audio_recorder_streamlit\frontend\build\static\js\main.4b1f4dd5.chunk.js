/*! For license information please see main.4b1f4dd5.chunk.js.LICENSE.txt */
(this.webpackJsonpstreamlit_component_template=this.webpackJsonpstreamlit_component_template||[]).push([[0],{19:function(t,e,r){t.exports=r(28)},28:function(t,e,r){"use strict";r.r(e);var n=r(7),o=r.n(n),a=r(15),i=r.n(a),c=r(4),s=r(0),u=r(1),l=r(8),f=r(2),h=r(3),p=r(10),d=r(18),g=r(16),v=r(13);function y(){y=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",a=n.asyncIterator||"@@asyncIterator",i=n.toStringTag||"@@toStringTag";function c(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(j){c=function(t,e,r){return t[e]=r}}function s(t,e,r,n){var o=e&&e.prototype instanceof f?e:f,a=Object.create(o.prototype),i=new E(n||[]);return a._invoke=function(t,e,r){var n="suspendedStart";return function(o,a){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw a;return O()}for(r.method=o,r.arg=a;;){var i=r.delegate;if(i){var c=b(i,r);if(c){if(c===l)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var s=u(t,e,r);if("normal"===s.type){if(n=r.done?"completed":"suspendedYield",s.arg===l)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(n="completed",r.method="throw",r.arg=s.arg)}}}(t,r,i),a}function u(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(j){return{type:"throw",arg:j}}}t.wrap=s;var l={};function f(){}function h(){}function p(){}var d={};c(d,o,(function(){return this}));var g=Object.getPrototypeOf,v=g&&g(g(k([])));v&&v!==e&&r.call(v,o)&&(d=v);var m=p.prototype=f.prototype=Object.create(d);function w(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function x(t,e){var n;this._invoke=function(o,a){function i(){return new e((function(n,i){!function n(o,a,i,c){var s=u(t[o],t,a);if("throw"!==s.type){var l=s.arg,f=l.value;return f&&"object"==typeof f&&r.call(f,"__await")?e.resolve(f.__await).then((function(t){n("next",t,i,c)}),(function(t){n("throw",t,i,c)})):e.resolve(f).then((function(t){l.value=t,i(l)}),(function(t){return n("throw",t,i,c)}))}c(s.arg)}(o,a,n,i)}))}return n=n?n.then(i,i):i()}}function b(t,e){var r=t.iterator[e.method];if(void 0===r){if(e.delegate=null,"throw"===e.method){if(t.iterator.return&&(e.method="return",e.arg=void 0,b(t,e),"throw"===e.method))return l;e.method="throw",e.arg=new TypeError("The iterator does not provide a 'throw' method")}return l}var n=u(r,t.iterator,e.arg);if("throw"===n.type)return e.method="throw",e.arg=n.arg,e.delegate=null,l;var o=n.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,l):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,l)}function _(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function L(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(_,this),this.reset(!0)}function k(t){if(t){var e=t[o];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,a=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return a.next=a}}return{next:O}}function O(){return{value:void 0,done:!0}}return h.prototype=p,c(m,"constructor",p),c(p,"constructor",h),h.displayName=c(p,i,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===h||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,p):(t.__proto__=p,c(t,i,"GeneratorFunction")),t.prototype=Object.create(m),t},t.awrap=function(t){return{__await:t}},w(x.prototype),c(x.prototype,a,(function(){return this})),t.AsyncIterator=x,t.async=function(e,r,n,o,a){void 0===a&&(a=Promise);var i=new x(s(e,r,n,o),a);return t.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},w(m),c(m,i,"Generator"),c(m,o,(function(){return this})),c(m,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=[];for(var r in t)e.push(r);return e.reverse(),function r(){for(;e.length;){var n=e.pop();if(n in t)return r.value=n,r.done=!1,r}return r.done=!0,r}},t.values=k,E.prototype={constructor:E,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(L),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(r,n){return i.type="throw",i.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],i=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var c=r.call(a,"catchLoc"),s=r.call(a,"finallyLoc");if(c&&s){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=t,i.arg=e,a?(this.method="next",this.next=a.finallyLoc,l):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),l},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),L(r),l}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;L(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:k(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),l}},t}p.b.add(d.a);var m=function(t){Object(f.a)(r,t);var e=Object(h.a)(r);function r(t){var n;return Object(u.a)(this,r),(n=e.call(this,t)).stream=null,n.AudioContext=window.AudioContext||window.webkitAudioContext,n.type="audio/wav",n.sampleRate=null,n.phrase_buffer_count=null,n.pause_buffer_count=null,n.pause_count=0,n.stage=null,n.volume=null,n.audioInput=null,n.analyser=null,n.recorder=null,n.recording=!1,n.leftchannel=[],n.rightchannel=[],n.leftBuffer=null,n.rightBuffer=null,n.recordingLength=0,n.tested=!1,n.getStream=function(){return navigator.mediaDevices.getUserMedia({audio:!0,video:!1})},n.setupMic=Object(c.a)(y().mark((function t(){return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,n.getStream();case 3:window.stream=n.stream=t.sent,t.next=9;break;case 6:t.prev=6,t.t0=t.catch(0),console.log("Error: Issue getting mic",t.t0);case 9:n.startRecording();case 10:case"end":return t.stop()}}),t,null,[[0,6]])}))),n.closeMic=function(){n.stream.getAudioTracks().forEach((function(t){t.stop()})),n.audioInput.disconnect(0),n.analyser.disconnect(0),n.recorder.disconnect(0)},n.writeUTFBytes=function(t,e,r){for(var n=r.length,o=0;o<n;o++)t.setUint8(e+o,r.charCodeAt(o))},n.mergeBuffers=function(t,e){for(var r=new Float32Array(e),n=0,o=t.length,a=0;a<o;a++){var i=t[a];r.set(i,n),n+=i.length}return r},n.interleave=function(t,e){for(var r=t.length+e.length,n=new Float32Array(r),o=0,a=0;a<r;)n[a++]=t[o],n[a++]=e[o],o++;return n},n.startRecording=function(){var t=n.props.args.sample_rate;null===t?(n.context=new n.AudioContext,n.sampleRate=n.context.sampleRate):(n.context=new n.AudioContext({sampleRate:t}),n.sampleRate=t),console.log("Sample rate ".concat(n.sampleRate,"Hz"));var e=2048/n.sampleRate;n.pause_buffer_count=Math.ceil(n.props.args.pause_threshold/e),n.pause_count=0,n.stage="start",n.volume=n.context.createGain(),n.audioInput=n.context.createMediaStreamSource(n.stream),n.analyser=n.context.createAnalyser(),n.audioInput.connect(n.analyser),n.recorder=n.context.createScriptProcessor(2048,2,2),n.analyser.connect(n.recorder),n.recorder.connect(n.context.destination);var r=Object(l.a)(n);n.recorder.onaudioprocess=function(t){if(r.recording){var e=t.inputBuffer.getChannelData(0),n=t.inputBuffer.getChannelData(1);r.tested||(r.tested=!0,e.reduce((function(t,e){return t+e}))||(console.log("Error: There seems to be an issue with your Mic"),r.stop(),r.stream.getTracks().forEach((function(t){t.stop()})),r.context.close()));var o=Math.sqrt(e.map((function(t){return t*t})).reduce((function(t,e){return t+e}))/e.length);"start"===r.stage&&o>r.props.args.start_threshold?r.stage="speaking":"speaking"===r.stage&&(o>r.props.args.end_threshold?r.pause_count=0:(r.pause_count+=1,r.pause_count>r.pause_buffer_count&&r.stop())),r.leftchannel.push(new Float32Array(e)),r.rightchannel.push(new Float32Array(n)),r.recordingLength+=2048}}},n.start=Object(c.a)(y().mark((function t(){return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n.recording=!0,n.setState({color:n.props.args.recording_color}),t.next=4,n.setupMic();case 4:n.leftchannel.length=n.rightchannel.length=0,n.recordingLength=0;case 6:case"end":return t.stop()}}),t)}))),n.stop=Object(c.a)(y().mark((function t(){var e,r,o,a,i,c,s,u;return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:for(n.recording=!1,n.setState({color:n.props.args.neutral_color}),n.closeMic(),console.log(n.recordingLength),n.leftBuffer=n.mergeBuffers(n.leftchannel,n.recordingLength),n.rightBuffer=n.mergeBuffers(n.rightchannel,n.recordingLength),e=n.interleave(n.leftBuffer,n.rightBuffer),r=new ArrayBuffer(44+2*e.length),o=new DataView(r),n.writeUTFBytes(o,0,"RIFF"),o.setUint32(4,44+2*e.length,!0),n.writeUTFBytes(o,8,"WAVE"),n.writeUTFBytes(o,12,"fmt "),o.setUint32(16,16,!0),o.setUint16(20,1,!0),o.setUint16(22,2,!0),o.setUint32(24,n.sampleRate,!0),o.setUint32(28,4*n.sampleRate,!0),o.setUint16(32,4,!0),o.setUint16(34,16,!0),n.writeUTFBytes(o,36,"data"),o.setUint32(40,2*e.length,!0),a=e.length,i=44,1,c=0;c<a;c++)o.setInt16(i,32767*e[c],!0),i+=2;return s=new Blob([o],{type:n.type}),u=URL.createObjectURL(s),t.next=30,n.onStop({blob:s,url:u,type:n.type});case 30:case"end":return t.stop()}}),t)}))),n.render=function(){n.props.theme;var t=n.props.args.text;return o.a.createElement("span",null,t," \xa0",o.a.createElement(g.a,{icon:n.props.args.icon_name,onClick:n.onClicked,style:{color:n.state.color},size:n.props.args.icon_size}))},n.onClicked=Object(c.a)(y().mark((function t(){return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(n.recording){t.next=5;break}return t.next=3,n.start();case 3:t.next=7;break;case 5:return t.next=7,n.stop();case 7:case"end":return t.stop()}}),t)}))),n.onStop=function(){var t=Object(c.a)(y().mark((function t(e){var r,n;return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.blob.arrayBuffer();case 2:r=t.sent,n=JSON.stringify(Array.from(new Uint8Array(r))),v.a.setComponentValue(n);case 5:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),n.state={color:n.props.args.neutral_color},n}return Object(s.a)(r)}(v.b),w=Object(v.c)(m);i.a.render(o.a.createElement(o.a.StrictMode,null,o.a.createElement(w,null)),document.getElementById("root"))}},[[19,1,2]]]);
//# sourceMappingURL=main.4b1f4dd5.chunk.js.map