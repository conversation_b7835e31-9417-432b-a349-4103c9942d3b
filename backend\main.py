# backend/main.py
from fastapi import FastAPI, File, UploadFile, Form, WebSocket
from fastapi.middleware.cors import CORSMiddleware
import os
import shutil
import json
from model_utils import generate_soap_note
from transcription import transcribe_audio

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Store conversation
conversation = []

@app.post("/analyze/")
async def analyze(audio: UploadFile = File(None), transcript: str = Form(None)):
    text = ""
    if audio:
        temp_audio_path = f"temp_{audio.filename}"
        with open(temp_audio_path, "wb") as buffer:
            shutil.copyfileobj(audio.file, buffer)
        text = transcribe_audio(temp_audio_path)
        os.remove(temp_audio_path)
        return {"transcribed_text": text}
    elif transcript:
        text = transcript
        soap_note = generate_soap_note(text)
        return {"soap_note": soap_note}
    else:
        return {"error": "No input provided."}

@app.post("/add_speech/")
async def add_speech(speaker: str = Form(...), text: str = Form(...)):
    conversation.append({"speaker": speaker, "text": text})
    return {"status": "added", "conversation": conversation}

@app.get("/conversation/")
async def get_conversation():
    return {"conversation": conversation}

@app.post("/generate_soap/")
async def generate_soap():
    if not conversation:
        return {"error": "No conversation to analyze"}

    # Format conversation for SOAP generation
    formatted_conversation = "\n".join([f"{item['speaker']}: {item['text']}" for item in conversation])
    soap_note = generate_soap_note(formatted_conversation)
    return {"soap_note": soap_note}

@app.post("/chat/")
async def chat_with_doctor(request: dict):
    """LLM Doctor Response"""
    try:
        patient_message = request.get("message", "")

        # Simple medical AI responses based on keywords
        patient_lower = patient_message.lower()

        if "hello" in patient_lower or "hi" in patient_lower:
            response = "Hello! I'm Dr. Smith. What brings you in today? Please describe your symptoms in detail."
        elif "headache" in patient_lower:
            response = "I understand you're experiencing headaches. Can you tell me more about the severity and frequency? Have you noticed any triggers?"
        elif "pain" in patient_lower:
            response = "I see you're in pain. Can you describe the type of pain - is it sharp, dull, or throbbing? Where exactly is it located?"
        elif "fever" in patient_lower:
            response = "A fever can indicate an infection. Have you taken your temperature? Any other symptoms like chills or body aches?"
        elif "cough" in patient_lower:
            response = "How long have you had this cough? Is it dry or productive? Any chest pain or shortness of breath?"
        elif "tired" in patient_lower or "fatigue" in patient_lower:
            response = "Fatigue can have many causes. How long have you been feeling tired? Are you getting enough sleep?"
        else:
            response = "Thank you for sharing that. Can you provide more specific details about your symptoms and when they started?"

        return {"response": response}
    except Exception as e:
        return {"response": "I understand. Can you tell me more about how you're feeling?"}

@app.post("/clear_conversation/")
async def clear_conversation():
    global conversation
    conversation = []
    return {"status": "cleared"}
