# backend/main.py
from fastapi import FastAPI, File, UploadFile, Form, WebSocket
from fastapi.middleware.cors import CORSMiddleware
import os
import shutil
import json
from model_utils import generate_soap_note
from transcription import transcribe_audio

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Store conversation
conversation = []

@app.post("/analyze/")
async def analyze(audio: UploadFile = File(None), transcript: str = Form(None)):
    text = ""
    if audio:
        temp_audio_path = f"temp_{audio.filename}"
        with open(temp_audio_path, "wb") as buffer:
            shutil.copyfileobj(audio.file, buffer)
        text = transcribe_audio(temp_audio_path)
        os.remove(temp_audio_path)
        return {"transcribed_text": text}
    elif transcript:
        text = transcript
        soap_note = generate_soap_note(text)
        return {"soap_note": soap_note}
    else:
        return {"error": "No input provided."}

@app.post("/add_speech/")
async def add_speech(speaker: str = Form(...), text: str = Form(...)):
    conversation.append({"speaker": speaker, "text": text})
    return {"status": "added", "conversation": conversation}

@app.get("/conversation/")
async def get_conversation():
    return {"conversation": conversation}

@app.post("/generate_soap/")
async def generate_soap():
    if not conversation:
        return {"error": "No conversation to analyze"}

    # Format conversation for SOAP generation
    formatted_conversation = "\n".join([f"{item['speaker']}: {item['text']}" for item in conversation])
    soap_note = generate_soap_note(formatted_conversation)
    return {"soap_note": soap_note}

@app.post("/clear_conversation/")
async def clear_conversation():
    global conversation
    conversation = []
    return {"status": "cleared"}
