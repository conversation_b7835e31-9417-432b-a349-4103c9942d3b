{"version": 3, "sources": ["AudioRecorder.tsx", "index.tsx"], "names": ["library", "add", "fas", "AudioRecorder", "props", "stream", "AudioContext", "window", "webkitAudioContext", "type", "sampleRate", "phrase_buffer_count", "pause_buffer_count", "pause_count", "stage", "volume", "audioInput", "analyser", "recorder", "recording", "leftchannel", "rightchannel", "left<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "recordingLength", "tested", "getStream", "navigator", "mediaDevices", "getUserMedia", "audio", "video", "setupMic", "console", "log", "startRecording", "closeMic", "getAudioTracks", "for<PERSON>ach", "track", "stop", "disconnect", "writeUTFBytes", "view", "offset", "string", "lng", "length", "i", "setUint8", "charCodeAt", "mergeBuffers", "channelBuffer", "result", "Float32Array", "buffer", "set", "interleave", "leftChannel", "rightChannel", "inputIndex", "index", "input_sample_rate", "args", "context", "seconds_per_buffer", "Math", "ceil", "createGain", "createMediaStreamSource", "create<PERSON><PERSON>yser", "connect", "createScriptProcessor", "destination", "self", "onaudioprocess", "e", "left", "inputBuffer", "getChannelData", "right", "reduce", "a", "b", "getTracks", "close", "energy", "sqrt", "map", "x", "push", "start", "setState", "color", "interleaved", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DataView", "setUint32", "setUint16", "setInt16", "blob", "Blob", "audioUrl", "URL", "createObjectURL", "onStop", "url", "render", "theme", "text", "icon", "onClick", "onClicked", "style", "state", "size", "data", "arrayBuffer", "json_string", "JSON", "stringify", "Array", "from", "Uint8Array", "Streamlit", "setComponentValue", "StreamlitComponentBase", "withStreamlitConnection", "ReactDOM", "StrictMode", "document", "getElementById"], "mappings": ";glNAUAA,IAAQC,IAAIC,K,IAmBNC,E,kDACJ,WAAmBC,GAA4B,IAAD,8BAC5C,cAAMA,IAIRC,OAA6B,KALiB,EAM9CC,aAAeC,OAAOD,cAAgBC,OAAOC,mBANC,EAO9CC,KAAe,YAP+B,EAQ9CC,WAA4B,KARkB,EAS9CC,oBAAqC,KATS,EAU9CC,mBAAoC,KAVU,EAW9CC,YAAsB,EAXwB,EAY9CC,MAAuB,KAZuB,EAa9CC,OAAc,KAbgC,EAc9CC,WAAkB,KAd4B,EAe9CC,SAAgB,KAf8B,EAgB9CC,SAAgB,KAhB8B,EAiB9CC,WAAqB,EAjByB,EAkB9CC,YAA8B,GAlBgB,EAmB9CC,aAA+B,GAnBe,EAoB9CC,WAAkC,KApBY,EAqB9CC,YAAmC,KArBW,EAsB9CC,gBAA0B,EAtBoB,EAuB9CC,QAAkB,EAvB4B,EA0B9CC,UAAY,WACV,OAAOC,UAAUC,aAAaC,aAAa,CAAEC,OAAO,EAAMC,OAAO,KA3BrB,EA8B9CC,SA9B8C,sBA8BnC,uGAE6B,EAAKN,YAFlC,OAEPnB,OAAOF,OAAS,EAAKA,OAFd,sDAIP4B,QAAQC,IAAI,2BAAZ,MAJO,OAOT,EAAKC,iBAPI,yDA9BmC,EAwC9CC,SAAW,WACT,EAAK/B,OAAQgC,iBAAiBC,SAAQ,SAACC,GACrCA,EAAMC,UAER,EAAKxB,WAAWyB,WAAW,GAC3B,EAAKxB,SAASwB,WAAW,GACzB,EAAKvB,SAASuB,WAAW,IA9CmB,EAiD9CC,cAAgB,SAACC,EAAgBC,EAAgBC,GAE/C,IADA,IAAIC,EAAMD,EAAOE,OACRC,EAAI,EAAGA,EAAIF,EAAKE,IACvBL,EAAKM,SAASL,EAASI,EAAGH,EAAOK,WAAWF,KApDF,EAwD9CG,aAAe,SAACC,EAA+B5B,GAI7C,IAHA,IAAI6B,EAAS,IAAIC,aAAa9B,GAC1BoB,EAAS,EACTE,EAAMM,EAAcL,OACfC,EAAI,EAAGA,EAAIF,EAAKE,IAAK,CAC5B,IAAIO,EAASH,EAAcJ,GAC3BK,EAAOG,IAAID,EAAQX,GACnBA,GAAUW,EAAOR,OAEnB,OAAOM,GAjEqC,EAoE9CI,WAAa,SAACC,EAA2BC,GAMvC,IALA,IAAIZ,EAASW,EAAYX,OAASY,EAAaZ,OAC3CM,EAAS,IAAIC,aAAaP,GAE1Ba,EAAa,EAERC,EAAQ,EAAGA,EAAQd,GAC1BM,EAAOQ,KAAWH,EAAYE,GAC9BP,EAAOQ,KAAWF,EAAaC,GAC/BA,IAEF,OAAOP,GA/EqC,EAkF9ClB,eAAiB,WACf,IAAI2B,EAAoB,EAAK1D,MAAM2D,KAAX,YACE,OAAtBD,GACF,EAAKE,QAAU,IAAI,EAAK1D,aACxB,EAAKI,WAAa,EAAKsD,QAAQtD,aAE/B,EAAKsD,QAAU,IAAI,EAAK1D,aACtB,CAAC,WAAcwD,IAEjB,EAAKpD,WAAaoD,GAEpB7B,QAAQC,IAAR,sBAA2B,EAAKxB,WAAhC,OAGA,IACIuD,EADa,KACqB,EAAKvD,WAC3C,EAAKE,mBAAqBsD,KAAKC,KAC7B,EAAK/D,MAAM2D,KAAX,gBAAqCE,GAEvC,EAAKpD,YAAc,EACnB,EAAKC,MAAQ,QAGb,EAAKC,OAAS,EAAKiD,QAAQI,aAG3B,EAAKpD,WAAa,EAAKgD,QAAQK,wBAAwB,EAAKhE,QAG5D,EAAKY,SAAW,EAAK+C,QAAQM,iBAG7B,EAAKtD,WAAWuD,QAAQ,EAAKtD,UAK7B,EAAKC,SAAW,EAAK8C,QAAQQ,sBAvBZ,KAuB8C,EAAG,GAKlE,EAAKvD,SAASsD,QAAQ,EAAKrD,UAG3B,EAAKA,SAASqD,QAAQ,EAAKP,QAAQS,aAEnC,IAAMC,EAAI,eACV,EAAKxD,SAASyD,eAAiB,SAAUC,GAEvC,GAAKF,EAAKvD,UAAV,CAEA,IAAI0D,EAAOD,EAAEE,YAAYC,eAAe,GACpCC,EAAQJ,EAAEE,YAAYC,eAAe,GACpCL,EAAKjD,SACRiD,EAAKjD,QAAS,EAEToD,EAAKI,QAAO,SAACC,EAAWC,GAAZ,OAA0BD,EAAIC,OAC7ClD,QAAQC,IAAI,mDAEZwC,EAAKlC,OACLkC,EAAKrE,OAAQ+E,YAAY9C,SAAQ,SAAUC,GACzCA,EAAMC,UAERkC,EAAKV,QAAQqB,UAIjB,IAAIC,EAASpB,KAAKqB,KAChBV,EAAKW,KAAI,SAACC,GAAD,OAAeA,EAAIA,KAAGR,QAAO,SAACC,EAAWC,GAAZ,OAA0BD,EAAIC,KAAKN,EAAK9B,QAE7D,UAAf2B,EAAK5D,OAAqBwE,EAASZ,EAAKtE,MAAM2D,KAAX,gBACrCW,EAAK5D,MAAQ,WACW,aAAf4D,EAAK5D,QACVwE,EAASZ,EAAKtE,MAAM2D,KAAX,cACXW,EAAK7D,YAAc,GAEnB6D,EAAK7D,aAAe,EAChB6D,EAAK7D,YAAc6D,EAAK9D,oBAC1B8D,EAAKlC,SAQXkC,EAAKtD,YAAYsE,KAAK,IAAIpC,aAAauB,IACvCH,EAAKrD,aAAaqE,KAAK,IAAIpC,aAAa0B,IACxCN,EAAKlD,iBA3EU,QAhG2B,EAgL9CmE,MAhL8C,sBAgLtC,qFACN,EAAKxE,WAAY,EACjB,EAAKyE,SAAS,CACZC,MAAO,EAAKzF,MAAM2D,KAAX,kBAHH,SAKA,EAAK/B,WALL,OAON,EAAKZ,YAAY2B,OAAS,EAAK1B,aAAa0B,OAAS,EACrD,EAAKvB,gBAAkB,EARjB,2CAhLsC,EA2L9CgB,KA3L8C,sBA2LvC,kGA+CL,IA9CA,EAAKrB,WAAY,EACjB,EAAKyE,SAAS,CACZC,MAAO,EAAKzF,MAAM2D,KAAX,gBAET,EAAK3B,WACLH,QAAQC,IAAI,EAAKV,iBAGjB,EAAKF,WAAa,EAAK6B,aAAa,EAAK/B,YAAa,EAAKI,iBAC3D,EAAKD,YAAc,EAAK4B,aACtB,EAAK9B,aACL,EAAKG,iBAGHsE,EAAc,EAAKrC,WAAW,EAAKnC,WAAY,EAAKC,aAOpDgC,EAAS,IAAIwC,YAAY,GAA0B,EAArBD,EAAY/C,QAC1CJ,EAAO,IAAIqD,SAASzC,GAGxB,EAAKb,cAAcC,EAAM,EAAG,QAC5BA,EAAKsD,UAAU,EAAG,GAA0B,EAArBH,EAAY/C,QAAY,GAC/C,EAAKL,cAAcC,EAAM,EAAG,QAE5B,EAAKD,cAAcC,EAAM,GAAI,QAC7BA,EAAKsD,UAAU,GAAI,IAAI,GACvBtD,EAAKuD,UAAU,GAAI,GAAG,GAEtBvD,EAAKuD,UAAU,GAAI,GAAG,GACtBvD,EAAKsD,UAAU,GAAI,EAAKvF,YAAa,GACrCiC,EAAKsD,UAAU,GAAuB,EAAnB,EAAKvF,YAAiB,GACzCiC,EAAKuD,UAAU,GAAI,GAAG,GACtBvD,EAAKuD,UAAU,GAAI,IAAI,GAEvB,EAAKxD,cAAcC,EAAM,GAAI,QAC7BA,EAAKsD,UAAU,GAAyB,EAArBH,EAAY/C,QAAY,GAGvCD,EAAMgD,EAAY/C,OAClBc,EAAQ,GACC,EACJb,EAAI,EAAGA,EAAIF,EAAKE,IACvBL,EAAKwD,SAAStC,EAAO,MAAAiC,EAAY9C,IAAwB,GACzDa,GAAS,EAjDN,OAqDCuC,EAAO,IAAIC,KAAK,CAAC1D,GAAO,CAAElC,KAAM,EAAKA,OACrC6F,EAAWC,IAAIC,gBAAgBJ,GAtDhC,UAyDC,EAAKK,OAAO,CAChBL,KAAMA,EACNM,IAAKJ,EACL7F,KAAM,EAAKA,OA5DR,4CA3LuC,EA2PvCkG,OAAS,WACI,EAAKvG,MAAfwG,MAAR,IACMC,EAAO,EAAKzG,MAAM2D,KAAX,KAOb,OACE,8BACG8C,EADH,QAEE,kBAAC,IAAD,CAEAC,KAAM,EAAK1G,MAAM2D,KAAX,UACNgD,QAAS,EAAKC,UACdC,MAAO,CAACpB,MAAM,EAAKqB,MAAMrB,OACzBsB,KAAM,EAAK/G,MAAM2D,KAAX,cA5QkC,EAkRtCiD,UAlRsC,sBAkR1B,iFACb,EAAK7F,UADQ,gCAEV,EAAKwE,QAFK,6CAIV,EAAKnD,OAJK,2CAlR0B,EA2RtCiE,OA3RsC,uCA2R7B,WAAOW,GAAP,yFACIA,EAAKhB,KAAKiB,cADd,OACX9D,EADW,OAEX+D,EAAcC,KAAKC,UAAUC,MAAMC,KAAK,IAAIC,WAAWpE,KAC3DqE,IAAUC,kBAAkBP,GAHb,2CA3R6B,sDAE5C,EAAKJ,MAAQ,CAAErB,MAAO,EAAKzF,MAAM2D,KAAX,eAFsB,E,uBADpB+D,KAoSbC,cAAwB5H,GC7TvC6H,IAASrB,OACP,kBAAC,IAAMsB,WAAP,KACE,kBAAC,EAAD,OAEFC,SAASC,eAAe,W", "file": "static/js/main.4b1f4dd5.chunk.js", "sourcesContent": ["import { library } from '@fortawesome/fontawesome-svg-core';\nimport { fas } from \"@fortawesome/free-solid-svg-icons\";\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\nimport React, { ReactNode } from \"react\";\nimport {\n  Streamlit,\n  StreamlitComponentBase,\n  withStreamlitConnection\n} from \"streamlit-component-lib\";\n\nlibrary.add(fas)\n\ninterface AudioRecorderState {\n  color: string\n}\n\ninterface AudioData {\n  blob: Blob\n  url: string\n  type: string\n}\n\ninterface AudioRecorderProps {\n  args: Map<string, any>\n  width: number\n  disabled: boolean\n}\n\n\nclass AudioRecorder extends StreamlitComponentBase<AudioRecorderState> {\n  public constructor(props: AudioRecorderProps) {\n    super(props)\n    this.state = { color: this.props.args[\"neutral_color\"] }\n  }\n\n  stream: MediaStream | null = null;\n  AudioContext = window.AudioContext || window.webkitAudioContext;\n  type: string = \"audio/wav\";\n  sampleRate: number | null = null;\n  phrase_buffer_count: number | null = null;\n  pause_buffer_count: number | null = null;\n  pause_count: number = 0;\n  stage: string | null = null;\n  volume: any = null;\n  audioInput: any = null;\n  analyser: any = null;\n  recorder: any = null;\n  recording: boolean = false;\n  leftchannel: Float32Array[] = [];\n  rightchannel: Float32Array[] = [];\n  leftBuffer: Float32Array | null = null;\n  rightBuffer: Float32Array | null = null;\n  recordingLength: number = 0;\n  tested: boolean = false;\n\n  //get mic stream\n  getStream = (): Promise<MediaStream> => {\n    return navigator.mediaDevices.getUserMedia({ audio: true, video: false });\n  };\n\n  setupMic = async () => {\n    try {\n      window.stream = this.stream = await this.getStream();\n    } catch (err) {\n      console.log(\"Error: Issue getting mic\", err);\n    }\n\n    this.startRecording();\n  };\n\n  closeMic = () => {\n    this.stream!.getAudioTracks().forEach((track) => {\n      track.stop();\n    });\n    this.audioInput.disconnect(0);\n    this.analyser.disconnect(0);\n    this.recorder.disconnect(0);\n  };\n\n  writeUTFBytes = (view: DataView, offset: number, string: string) => {\n    let lng = string.length;\n    for (let i = 0; i < lng; i++) {\n      view.setUint8(offset + i, string.charCodeAt(i));\n    }\n  };\n\n  mergeBuffers = (channelBuffer: Float32Array[], recordingLength: number) => {\n    let result = new Float32Array(recordingLength);\n    let offset = 0;\n    let lng = channelBuffer.length;\n    for (let i = 0; i < lng; i++) {\n      let buffer = channelBuffer[i];\n      result.set(buffer, offset);\n      offset += buffer.length;\n    }\n    return result;\n  };\n\n  interleave = (leftChannel: Float32Array, rightChannel: Float32Array) => {\n    let length = leftChannel.length + rightChannel.length;\n    let result = new Float32Array(length);\n\n    let inputIndex = 0;\n\n    for (let index = 0; index < length; ) {\n      result[index++] = leftChannel[inputIndex];\n      result[index++] = rightChannel[inputIndex];\n      inputIndex++;\n    }\n    return result;\n  };\n\n  startRecording = () => {\n    let input_sample_rate = this.props.args[\"sample_rate\"];\n    if (input_sample_rate === null) {\n      this.context = new this.AudioContext();\n      this.sampleRate = this.context.sampleRate;\n    } else {\n      this.context = new this.AudioContext(\n        {\"sampleRate\": input_sample_rate}\n      );\n      this.sampleRate = input_sample_rate;\n    }\n    console.log(`Sample rate ${this.sampleRate}Hz`);\n\n    // create buffer states counts\n    let bufferSize = 2048;\n    let seconds_per_buffer = bufferSize / this.sampleRate!;\n    this.pause_buffer_count = Math.ceil(\n      this.props.args[\"pause_threshold\"] / seconds_per_buffer\n    );\n    this.pause_count = 0;\n    this.stage = \"start\";\n\n    // creates a gain node\n    this.volume = this.context.createGain();\n\n    // creates an audio node from teh microphone incoming stream\n    this.audioInput = this.context.createMediaStreamSource(this.stream);\n\n    // Create analyser\n    this.analyser = this.context.createAnalyser();\n\n    // connect audio input to the analyser\n    this.audioInput.connect(this.analyser);\n\n    // connect analyser to the volume control\n    // analyser.connect(volume);\n\n    this.recorder = this.context.createScriptProcessor(bufferSize, 2, 2);\n\n    // we connect the volume control to the processor\n    // volume.connect(recorder);\n\n    this.analyser.connect(this.recorder);\n\n    // finally connect the processor to the output\n    this.recorder.connect(this.context.destination);\n\n    const self = this;  // to reference component from inside the function\n    this.recorder.onaudioprocess = function (e: any) {\n      // Check\n      if (!self.recording) return;\n      // Do something with the data, i.e Convert this to WAV\n      let left = e.inputBuffer.getChannelData(0);\n      let right = e.inputBuffer.getChannelData(1);\n      if (!self.tested) {\n        self.tested = true;\n        // if this reduces to 0 we are not getting any sound\n        if (!left.reduce((a: number, b: number) => a + b)) {\n          console.log(\"Error: There seems to be an issue with your Mic\");\n          // clean up;\n          self.stop();\n          self.stream!.getTracks().forEach(function (track: any) {\n            track.stop();\n          });\n          self.context.close();\n        }\n      }\n      // Check energy level\n      let energy = Math.sqrt(\n        left.map((x: number) => x * x).reduce((a: number, b: number) => a + b) / left.length\n      );\n      if (self.stage === \"start\" && energy > self.props.args[\"start_threshold\"]) {\n        self.stage = \"speaking\";\n      } else if (self.stage === \"speaking\") {\n        if (energy > self.props.args[\"end_threshold\"]) {\n          self.pause_count = 0;\n        } else {\n          self.pause_count += 1;\n          if (self.pause_count > self.pause_buffer_count!) {\n            self.stop();\n          }\n        }\n      }\n      // let radius = 33.0 + Math.sqrt(1000.0 * energy);\n      // this.props.setRadius(radius.toString());\n\n      // we clone the samples\n      self.leftchannel.push(new Float32Array(left));\n      self.rightchannel.push(new Float32Array(right));\n      self.recordingLength += bufferSize;\n    };\n    // this.visualize();\n  };\n\n  start = async () => {\n    this.recording = true;\n    this.setState({\n      color: this.props.args[\"recording_color\"]\n    })\n    await this.setupMic();\n    // reset the buffers for the new recording\n    this.leftchannel.length = this.rightchannel.length = 0;\n    this.recordingLength = 0;\n  };\n\n  stop = async () => {\n    this.recording = false;\n    this.setState({\n      color: this.props.args[\"neutral_color\"]\n    })\n    this.closeMic();\n    console.log(this.recordingLength);\n\n    // we flat the left and right channels down\n    this.leftBuffer = this.mergeBuffers(this.leftchannel, this.recordingLength);\n    this.rightBuffer = this.mergeBuffers(\n      this.rightchannel,\n      this.recordingLength\n    );\n    // we interleave both channels together\n    let interleaved = this.interleave(this.leftBuffer, this.rightBuffer);\n\n    ///////////// WAV Encode /////////////////\n    // from http://typedarray.org/from-microphone-to-wav-with-getusermedia-and-web-audio/\n    //\n\n    // we create our wav file\n    let buffer = new ArrayBuffer(44 + interleaved.length * 2);\n    let view = new DataView(buffer);\n\n    // RIFF chunk descriptor\n    this.writeUTFBytes(view, 0, \"RIFF\");\n    view.setUint32(4, 44 + interleaved.length * 2, true);\n    this.writeUTFBytes(view, 8, \"WAVE\");\n    // FMT sub-chunk\n    this.writeUTFBytes(view, 12, \"fmt \");\n    view.setUint32(16, 16, true);\n    view.setUint16(20, 1, true);\n    // stereo (2 channels)\n    view.setUint16(22, 2, true);\n    view.setUint32(24, this.sampleRate!, true);\n    view.setUint32(28, this.sampleRate! * 4, true);\n    view.setUint16(32, 4, true);\n    view.setUint16(34, 16, true);\n    // data sub-chunk\n    this.writeUTFBytes(view, 36, \"data\");\n    view.setUint32(40, interleaved.length * 2, true);\n\n    // write the PCM samples\n    let lng = interleaved.length;\n    let index = 44;\n    let volume = 1;\n    for (let i = 0; i < lng; i++) {\n      view.setInt16(index, interleaved[i] * (0x7fff * volume), true);\n      index += 2;\n    }\n\n    // our final binary blob\n    const blob = new Blob([view], { type: this.type });\n    const audioUrl = URL.createObjectURL(blob);\n\n\n    await this.onStop({\n      blob: blob,\n      url: audioUrl,\n      type: this.type,\n    });\n  };\n\n  public render = (): ReactNode => {\n    const { theme } = this.props\n    const text = this.props.args[\"text\"]\n\n    if (theme) {\n      // Maintain compatibility with older versions of Streamlit that don't send\n      // a theme object.\n    }\n\n    return (\n      <span>\n        {text} &nbsp;\n        <FontAwesomeIcon\n        // @ts-ignore\n        icon={this.props.args[\"icon_name\"]}\n        onClick={this.onClicked}\n        style={{color:this.state.color}}\n        size={this.props.args[\"icon_size\"]}\n        />\n      </span>\n    )\n  }\n\n  private onClicked = async () => {\n    if (!this.recording){\n      await this.start()\n    } else {\n      await this.stop()\n    }\n\n  }\n\n  private onStop = async (data: AudioData) => {\n    var buffer = await data.blob.arrayBuffer();\n    var json_string = JSON.stringify(Array.from(new Uint8Array(buffer)));\n    Streamlit.setComponentValue(json_string);\n  }\n\n}\n\nexport default withStreamlitConnection(AudioRecorder)\n", "import React from \"react\"\nimport ReactDOM from \"react-dom\"\nimport AudioRecorder from \"./AudioRecorder\"\n\nReactDOM.render(\n  <React.StrictMode>\n    <AudioRecorder />\n  </React.StrictMode>,\n  document.getElementById(\"root\")\n)\n"], "sourceRoot": ""}