# frontend/voice_app.py - <PERSON> Style Voice SOAP App
import streamlit as st
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from voice_conversation import VoiceConversationSystem
import threading
import time
import tempfile
import whisper
from gtts import gTTS
import pygame
from groq import Groq

st.set_page_config(
    page_title="🎤 Harry Bhai Style Voice SOAP", 
    layout="wide",
    initial_sidebar_state="collapsed"
)

# Custom CSS for <PERSON>hai style
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }
    .conversation-box {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1rem;
        margin: 0.5rem 0;
        border-left: 4px solid #007bff;
    }
    .patient-msg {
        background: #e3f2fd;
        border-left: 4px solid #2196f3;
    }
    .doctor-msg {
        background: #f3e5f5;
        border-left: 4px solid #9c27b0;
    }
    .voice-status {
        background: #d4edda;
        border: 1px solid #c3e6cb;
        border-radius: 5px;
        padding: 0.75rem;
        margin: 1rem 0;
        text-align: center;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
if 'voice_system' not in st.session_state:
    st.session_state.voice_system = None
if 'conversation_active' not in st.session_state:
    st.session_state.conversation_active = False
if 'conversation_history' not in st.session_state:
    st.session_state.conversation_history = []

def initialize_voice_system():
    """Initialize the voice conversation system"""
    try:
        # Get Groq API key from user
        groq_key = st.session_state.get('groq_api_key', '')
        
        if not groq_key:
            st.error("Please enter your Groq API key first!")
            return False
        
        with st.spinner("🚀 Initializing Harry Bhai Style Voice System..."):
            st.session_state.voice_system = VoiceConversationSystem(groq_api_key=groq_key)
            st.success("✅ Voice System Ready!")
            return True
    except Exception as e:
        st.error(f"❌ Failed to initialize: {e}")
        return False

def start_voice_conversation():
    """Start the voice conversation"""
    if not st.session_state.voice_system:
        if not initialize_voice_system():
            return
    
    st.session_state.conversation_active = True
    
    # Welcome message
    welcome_msg = "Hello! I'm Dr. Smith. I'm here to help you today. Please tell me what's bothering you."
    
    # Add to conversation history
    st.session_state.conversation_history.append({
        "speaker": "Doctor",
        "text": welcome_msg,
        "timestamp": time.strftime('%H:%M:%S')
    })
    
    # Speak welcome message
    try:
        tts = gTTS(text=welcome_msg, lang='en', slow=False)
        with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as temp_audio:
            tts.save(temp_audio.name)
            st.audio(temp_audio.name, autoplay=True)
            os.unlink(temp_audio.name)
    except:
        pass

def process_voice_input(audio_data):
    """Process voice input and get doctor response"""
    if not st.session_state.voice_system or not audio_data:
        return
    
    try:
        # Save audio to temporary file
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
            temp_file.write(audio_data)
            temp_file.flush()
            
            # Transcribe using Whisper
            result = st.session_state.voice_system.whisper_model.transcribe(temp_file.name)
            patient_text = result["text"].strip()
            
            os.unlink(temp_file.name)
            
            if patient_text:
                # Add patient message
                st.session_state.conversation_history.append({
                    "speaker": "Patient",
                    "text": patient_text,
                    "timestamp": time.strftime('%H:%M:%S')
                })
                
                # Generate doctor response
                doctor_response = st.session_state.voice_system.generate_doctor_response(patient_text)
                
                # Add doctor response
                st.session_state.conversation_history.append({
                    "speaker": "Doctor",
                    "text": doctor_response,
                    "timestamp": time.strftime('%H:%M:%S')
                })
                
                # Speak doctor response
                try:
                    tts = gTTS(text=doctor_response, lang='en', slow=False)
                    with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as temp_audio:
                        tts.save(temp_audio.name)
                        st.audio(temp_audio.name, autoplay=True)
                        os.unlink(temp_audio.name)
                except:
                    pass
                
                st.rerun()
                
    except Exception as e:
        st.error(f"❌ Error processing voice: {e}")

# Main App
def main():
    # Header
    st.markdown("""
    <div class="main-header">
        <h1>🎤 Harry Bhai Style Voice SOAP Generator</h1>
        <p>Real-time Voice Conversation with AI Doctor</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Sidebar for settings
    with st.sidebar:
        st.header("⚙️ Settings")
        
        # Groq API Key
        groq_key = st.text_input(
            "🔑 Groq API Key", 
            type="password",
            help="Get your free API key from https://console.groq.com"
        )
        if groq_key:
            st.session_state.groq_api_key = groq_key
        
        st.markdown("---")
        
        # System status
        if st.session_state.voice_system:
            st.success("✅ Voice System Ready")
        else:
            st.warning("⚠️ Voice System Not Initialized")
        
        if st.session_state.conversation_active:
            st.success("🔴 Conversation Active")
        else:
            st.info("⚪ Conversation Stopped")
    
    # Main content
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.header("🗣️ Voice Conversation")
        
        # Control buttons
        button_col1, button_col2, button_col3 = st.columns(3)
        
        with button_col1:
            if st.button("🚀 Start Conversation", disabled=st.session_state.conversation_active):
                start_voice_conversation()
        
        with button_col2:
            if st.button("🛑 Stop Conversation", disabled=not st.session_state.conversation_active):
                st.session_state.conversation_active = False
                st.success("Conversation stopped")
        
        with button_col3:
            if st.button("🗑️ Clear History"):
                st.session_state.conversation_history = []
                st.success("History cleared")
        
        # Voice input
        if st.session_state.conversation_active:
            st.markdown("""
            <div class="voice-status">
                🎤 <strong>Voice Recording Active</strong><br>
                Speak now - Doctor will respond automatically!
            </div>
            """, unsafe_allow_html=True)
            
            # Audio recorder
            from audio_recorder_streamlit import audio_recorder
            
            audio_data = audio_recorder(
                text="🎤 Speak to Doctor",
                recording_color="#e74c3c",
                neutral_color="#2196f3",
                icon_name="microphone",
                icon_size="3x",
                key=f"voice_input_{len(st.session_state.conversation_history)}"
            )
            
            if audio_data:
                process_voice_input(audio_data)
        
        # Conversation display
        st.subheader("💬 Live Conversation")
        
        if st.session_state.conversation_history:
            for msg in st.session_state.conversation_history:
                if msg["speaker"] == "Patient":
                    st.markdown(f"""
                    <div class="conversation-box patient-msg">
                        <strong>🤒 Patient [{msg['timestamp']}]:</strong><br>
                        {msg['text']}
                    </div>
                    """, unsafe_allow_html=True)
                else:
                    st.markdown(f"""
                    <div class="conversation-box doctor-msg">
                        <strong>👨‍⚕️ Doctor [{msg['timestamp']}]:</strong><br>
                        {msg['text']}
                    </div>
                    """, unsafe_allow_html=True)
        else:
            st.info("No conversation yet. Start talking to begin!")
    
    with col2:
        st.header("📋 SOAP Note")
        
        if len(st.session_state.conversation_history) > 2:
            if st.button("📝 Generate SOAP Note"):
                with st.spinner("Generating SOAP note..."):
                    # Format conversation for SOAP generation
                    conversation_text = "\n".join([
                        f"{msg['speaker']}: {msg['text']}" 
                        for msg in st.session_state.conversation_history
                    ])
                    
                    # Generate SOAP note (simplified)
                    soap_note = f"""
**SOAP Note - {time.strftime('%Y-%m-%d %H:%M')}**

**S – Subjective:**
{conversation_text}

**O – Objective:**
Clinical examination pending.

**A – Assessment:**
Based on patient consultation.

**P – Plan:**
Further evaluation and treatment plan to be determined.
"""
                    st.markdown(soap_note)
        else:
            st.info("Have a conversation first to generate SOAP note")
        
        # Instructions
        st.markdown("---")
        st.subheader("📖 How to Use")
        st.markdown("""
        1. **Enter Groq API Key** in sidebar
        2. **Click 'Start Conversation'**
        3. **Speak when prompted** 🎤
        4. **Doctor responds automatically** 🔊
        5. **Generate SOAP note** when done 📝
        
        **Features:**
        - ✅ Real-time voice recognition
        - ✅ AI doctor responses  
        - ✅ Natural conversation flow
        - ✅ Automatic SOAP generation
        """)

if __name__ == "__main__":
    main()
