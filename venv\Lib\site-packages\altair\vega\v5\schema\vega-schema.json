{"$schema": "http://json-schema.org/draft-06/schema#", "title": "Vega Visualization Specification Language", "defs": {"autosize": {"oneOf": [{"enum": ["pad", "fit", "fit-x", "fit-y", "none"], "default": "pad"}, {"type": "object", "properties": {"type": {"enum": ["pad", "fit", "fit-x", "fit-y", "none"], "default": "pad"}, "resize": {"type": "boolean"}, "contains": {"enum": ["content", "padding"]}}, "required": ["type"], "additionalProperties": false}, {"$ref": "#/refs/signal"}]}, "axis": {"type": "object", "properties": {"orient": {"enum": ["top", "bottom", "left", "right"]}, "scale": {"type": "string"}, "format": {"oneOf": [{"type": "string"}, {"type": "object", "properties": {"year": {"type": "string"}, "quarter": {"type": "string"}, "month": {"type": "string"}, "date": {"type": "string"}, "week": {"type": "string"}, "day": {"type": "string"}, "hours": {"type": "string"}, "minutes": {"type": "string"}, "seconds": {"type": "string"}, "milliseconds": {"type": "string"}}, "additionalProperties": false}, {"$ref": "#/refs/signal"}]}, "formatType": {"oneOf": [{"enum": ["number", "time", "utc"]}, {"$ref": "#/refs/signal"}]}, "minExtent": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "maxExtent": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "offset": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "position": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "bandPosition": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "translate": {"type": "number"}, "values": {"$ref": "#/refs/arrayOrSignal"}, "zindex": {"type": "number"}, "title": {"$ref": "#/refs/textOrSignal"}, "titlePadding": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "titleAlign": {"oneOf": [{"enum": ["left", "right", "center"]}, {"$ref": "#/refs/alignValue"}]}, "titleAnchor": {"oneOf": [{"enum": [null, "start", "middle", "end"]}, {"$ref": "#/refs/anchorValue"}]}, "titleAngle": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "titleX": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "titleY": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "titleBaseline": {"oneOf": [{"enum": ["top", "middle", "bottom", "alphabetic", "line-top", "line-bottom"]}, {"$ref": "#/refs/baselineValue"}]}, "titleColor": {"oneOf": [{"type": "null"}, {"type": "string"}, {"$ref": "#/refs/colorValue"}]}, "titleFont": {"oneOf": [{"type": "string"}, {"$ref": "#/refs/stringValue"}]}, "titleFontSize": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "titleFontStyle": {"oneOf": [{"type": "string"}, {"$ref": "#/refs/stringValue"}]}, "titleFontWeight": {"oneOf": [{"enum": [null, "normal", "bold", "lighter", "bolder", "100", "200", "300", "400", "500", "600", "700", "800", "900", 100, 200, 300, 400, 500, 600, 700, 800, 900]}, {"$ref": "#/refs/fontWeightValue"}]}, "titleLimit": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "titleLineHeight": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "titleOpacity": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "domain": {"type": "boolean"}, "domainColor": {"oneOf": [{"type": "null"}, {"type": "string"}, {"$ref": "#/refs/colorValue"}]}, "domainDash": {"oneOf": [{"type": "array", "items": {"type": "number"}}, {"$ref": "#/refs/arrayValue"}]}, "domainDashOffset": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "domainOpacity": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "domainWidth": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "ticks": {"type": "boolean"}, "tickBand": {"$ref": "#/refs/tickBand"}, "tickColor": {"oneOf": [{"type": "null"}, {"type": "string"}, {"$ref": "#/refs/colorValue"}]}, "tickDash": {"oneOf": [{"type": "array", "items": {"type": "number"}}, {"$ref": "#/refs/arrayValue"}]}, "tickDashOffset": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "tickOffset": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "tickOpacity": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "tickRound": {"oneOf": [{"type": "boolean"}, {"$ref": "#/refs/booleanValue"}]}, "tickSize": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "tickWidth": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "tickCount": {"$ref": "#/refs/tickCount"}, "tickExtra": {"$ref": "#/refs/booleanOrSignal"}, "tickMinStep": {"$ref": "#/refs/numberOrSignal"}, "grid": {"type": "boolean"}, "gridScale": {"type": "string"}, "gridColor": {"oneOf": [{"type": "null"}, {"type": "string"}, {"$ref": "#/refs/colorValue"}]}, "gridDash": {"oneOf": [{"type": "array", "items": {"type": "number"}}, {"$ref": "#/refs/arrayValue"}]}, "gridDashOffset": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "gridOpacity": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "gridWidth": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "labels": {"type": "boolean"}, "labelAlign": {"oneOf": [{"enum": ["left", "right", "center"]}, {"$ref": "#/refs/alignValue"}]}, "labelBaseline": {"oneOf": [{"enum": ["top", "middle", "bottom", "alphabetic", "line-top", "line-bottom"]}, {"$ref": "#/refs/baselineValue"}]}, "labelBound": {"oneOf": [{"type": "boolean"}, {"type": "number"}, {"$ref": "#/refs/signal"}]}, "labelFlush": {"oneOf": [{"type": "boolean"}, {"type": "number"}, {"$ref": "#/refs/signal"}]}, "labelFlushOffset": {"$ref": "#/refs/numberOrSignal"}, "labelOverlap": {"$ref": "#/refs/labelOverlap"}, "labelAngle": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "labelColor": {"oneOf": [{"type": "null"}, {"type": "string"}, {"$ref": "#/refs/colorValue"}]}, "labelFont": {"oneOf": [{"type": "string"}, {"$ref": "#/refs/stringValue"}]}, "labelFontSize": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "labelFontWeight": {"oneOf": [{"enum": [null, "normal", "bold", "lighter", "bolder", "100", "200", "300", "400", "500", "600", "700", "800", "900", 100, 200, 300, 400, 500, 600, 700, 800, 900]}, {"$ref": "#/refs/fontWeightValue"}]}, "labelFontStyle": {"oneOf": [{"type": "string"}, {"$ref": "#/refs/stringValue"}]}, "labelLimit": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "labelLineHeight": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "labelOpacity": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "labelOffset": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "labelPadding": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "labelSeparation": {"$ref": "#/refs/numberOrSignal"}, "encode": {"type": "object", "properties": {"axis": {"$ref": "#/defs/guideEncode"}, "ticks": {"$ref": "#/defs/guideEncode"}, "labels": {"$ref": "#/defs/guideEncode"}, "title": {"$ref": "#/defs/guideEncode"}, "grid": {"$ref": "#/defs/guideEncode"}, "domain": {"$ref": "#/defs/guideEncode"}}, "additionalProperties": false}}, "required": ["orient", "scale"], "additionalProperties": false}, "background": {"$ref": "#/refs/stringOrSignal"}, "bind": {"oneOf": [{"type": "object", "properties": {"input": {"enum": ["checkbox"]}, "element": {"$ref": "#/refs/element"}, "debounce": {"type": "number"}, "name": {"type": "string"}}, "required": ["input"], "additionalProperties": false}, {"type": "object", "properties": {"input": {"enum": ["radio", "select"]}, "element": {"$ref": "#/refs/element"}, "options": {"type": "array"}, "labels": {"type": "array", "items": {"type": "string"}}, "debounce": {"type": "number"}, "name": {"type": "string"}}, "required": ["input", "options"], "additionalProperties": false}, {"type": "object", "properties": {"input": {"enum": ["range"]}, "element": {"$ref": "#/refs/element"}, "min": {"type": "number"}, "max": {"type": "number"}, "step": {"type": "number"}, "debounce": {"type": "number"}, "name": {"type": "string"}}, "required": ["input"], "additionalProperties": false}, {"type": "object", "properties": {"input": {"not": {"enum": ["checkbox", "radio", "range", "select"]}}, "element": {"$ref": "#/refs/element"}, "debounce": {"type": "number"}, "name": {"type": "string"}}, "required": ["input"], "additionalProperties": true}]}, "data": {"oneOf": [{"type": "object", "properties": {"name": {"type": "string"}, "transform": {"type": "array", "items": {"$ref": "#/defs/transform"}}, "on": {"$ref": "#/defs/onTrigger"}}, "required": ["name"], "additionalProperties": false}, {"type": "object", "properties": {"source": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}, "minItems": 1}]}, "name": {"type": "string"}, "transform": {"type": "array", "items": {"$ref": "#/defs/transform"}}, "on": {"$ref": "#/defs/onTrigger"}}, "required": ["source", "name"], "additionalProperties": false}, {"type": "object", "properties": {"url": {"$ref": "#/refs/stringOrSignal"}, "format": {"oneOf": [{"anyOf": [{"type": "object", "properties": {"type": {"$ref": "#/refs/stringOrSignal"}, "parse": {"oneOf": [{"enum": ["auto"]}, {"type": "object", "properties": {}, "additionalProperties": {"oneOf": [{"enum": ["boolean", "number", "date", "string"]}, {"type": "string", "pattern": "^(date|utc):.*$"}]}}, {"$ref": "#/refs/signal"}]}}}, {"type": "object", "properties": {"type": {"enum": ["json"]}, "parse": {"oneOf": [{"enum": ["auto"]}, {"type": "object", "properties": {}, "additionalProperties": {"oneOf": [{"enum": ["boolean", "number", "date", "string"]}, {"type": "string", "pattern": "^(date|utc):.*$"}]}}, {"$ref": "#/refs/signal"}]}, "property": {"$ref": "#/refs/stringOrSignal"}, "copy": {"$ref": "#/refs/booleanOrSignal"}}, "additionalProperties": false}, {"type": "object", "properties": {"type": {"enum": ["csv", "tsv"]}, "header": {"type": "array", "items": {"type": "string"}}, "parse": {"oneOf": [{"enum": ["auto"]}, {"type": "object", "properties": {}, "additionalProperties": {"oneOf": [{"enum": ["boolean", "number", "date", "string"]}, {"type": "string", "pattern": "^(date|utc):.*$"}]}}, {"$ref": "#/refs/signal"}]}}, "required": ["type"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"enum": ["dsv"]}, "delimiter": {"type": "string"}, "header": {"type": "array", "items": {"type": "string"}}, "parse": {"oneOf": [{"enum": ["auto"]}, {"type": "object", "properties": {}, "additionalProperties": {"oneOf": [{"enum": ["boolean", "number", "date", "string"]}, {"type": "string", "pattern": "^(date|utc):.*$"}]}}, {"$ref": "#/refs/signal"}]}}, "required": ["type", "delimiter"], "additionalProperties": false}, {"oneOf": [{"type": "object", "properties": {"type": {"enum": ["<PERSON><PERSON><PERSON><PERSON>"]}, "feature": {"$ref": "#/refs/stringOrSignal"}, "property": {"$ref": "#/refs/stringOrSignal"}}, "required": ["type", "feature"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"enum": ["<PERSON><PERSON><PERSON><PERSON>"]}, "mesh": {"$ref": "#/refs/stringOrSignal"}, "property": {"$ref": "#/refs/stringOrSignal"}, "filter": {"enum": ["interior", "exterior", null]}}, "required": ["type", "mesh"], "additionalProperties": false}]}]}, {"$ref": "#/refs/signal"}]}, "async": {"$ref": "#/refs/booleanOrSignal"}, "name": {"type": "string"}, "transform": {"type": "array", "items": {"$ref": "#/defs/transform"}}, "on": {"$ref": "#/defs/onTrigger"}}, "required": ["url", "name"], "additionalProperties": false}, {"type": "object", "properties": {"values": {"oneOf": [{}, {"$ref": "#/refs/signal"}]}, "format": {"oneOf": [{"anyOf": [{"type": "object", "properties": {"type": {"$ref": "#/refs/stringOrSignal"}, "parse": {"oneOf": [{"enum": ["auto"]}, {"type": "object", "properties": {}, "additionalProperties": {"oneOf": [{"enum": ["boolean", "number", "date", "string"]}, {"type": "string", "pattern": "^(date|utc):.*$"}]}}, {"$ref": "#/refs/signal"}]}}}, {"type": "object", "properties": {"type": {"enum": ["json"]}, "parse": {"oneOf": [{"enum": ["auto"]}, {"type": "object", "properties": {}, "additionalProperties": {"oneOf": [{"enum": ["boolean", "number", "date", "string"]}, {"type": "string", "pattern": "^(date|utc):.*$"}]}}, {"$ref": "#/refs/signal"}]}, "property": {"$ref": "#/refs/stringOrSignal"}, "copy": {"$ref": "#/refs/booleanOrSignal"}}, "additionalProperties": false}, {"type": "object", "properties": {"type": {"enum": ["csv", "tsv"]}, "header": {"type": "array", "items": {"type": "string"}}, "parse": {"oneOf": [{"enum": ["auto"]}, {"type": "object", "properties": {}, "additionalProperties": {"oneOf": [{"enum": ["boolean", "number", "date", "string"]}, {"type": "string", "pattern": "^(date|utc):.*$"}]}}, {"$ref": "#/refs/signal"}]}}, "required": ["type"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"enum": ["dsv"]}, "delimiter": {"type": "string"}, "header": {"type": "array", "items": {"type": "string"}}, "parse": {"oneOf": [{"enum": ["auto"]}, {"type": "object", "properties": {}, "additionalProperties": {"oneOf": [{"enum": ["boolean", "number", "date", "string"]}, {"type": "string", "pattern": "^(date|utc):.*$"}]}}, {"$ref": "#/refs/signal"}]}}, "required": ["type", "delimiter"], "additionalProperties": false}, {"oneOf": [{"type": "object", "properties": {"type": {"enum": ["<PERSON><PERSON><PERSON><PERSON>"]}, "feature": {"$ref": "#/refs/stringOrSignal"}, "property": {"$ref": "#/refs/stringOrSignal"}}, "required": ["type", "feature"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"enum": ["<PERSON><PERSON><PERSON><PERSON>"]}, "mesh": {"$ref": "#/refs/stringOrSignal"}, "property": {"$ref": "#/refs/stringOrSignal"}, "filter": {"enum": ["interior", "exterior", null]}}, "required": ["type", "mesh"], "additionalProperties": false}]}]}, {"$ref": "#/refs/signal"}]}, "async": {"$ref": "#/refs/booleanOrSignal"}, "name": {"type": "string"}, "transform": {"type": "array", "items": {"$ref": "#/defs/transform"}}, "on": {"$ref": "#/defs/onTrigger"}}, "required": ["values", "name"], "additionalProperties": false}]}, "rule": {"type": "object", "properties": {"test": {"type": "string"}}}, "encodeEntry": {"type": "object", "properties": {"x": {"$ref": "#/refs/numberValue"}, "x2": {"$ref": "#/refs/numberValue"}, "xc": {"$ref": "#/refs/numberValue"}, "width": {"$ref": "#/refs/numberValue"}, "y": {"$ref": "#/refs/numberValue"}, "y2": {"$ref": "#/refs/numberValue"}, "yc": {"$ref": "#/refs/numberValue"}, "height": {"$ref": "#/refs/numberValue"}, "opacity": {"$ref": "#/refs/numberValue"}, "fill": {"$ref": "#/refs/colorValue"}, "fillOpacity": {"$ref": "#/refs/numberValue"}, "stroke": {"$ref": "#/refs/colorValue"}, "strokeOpacity": {"$ref": "#/refs/numberValue"}, "strokeWidth": {"$ref": "#/refs/numberValue"}, "strokeCap": {"$ref": "#/refs/strokeCapValue"}, "strokeDash": {"$ref": "#/refs/arrayValue"}, "strokeDashOffset": {"$ref": "#/refs/numberValue"}, "strokeJoin": {"$ref": "#/refs/strokeJoinValue"}, "strokeMiterLimit": {"$ref": "#/refs/numberValue"}, "blend": {"$ref": "#/refs/blendValue"}, "cursor": {"$ref": "#/refs/stringValue"}, "tooltip": {"$ref": "#/refs/anyValue"}, "zindex": {"$ref": "#/refs/numberValue"}, "clip": {"$ref": "#/refs/booleanValue"}, "strokeForeground": {"$ref": "#/refs/booleanValue"}, "strokeOffset": {"$ref": "#/refs/numberValue"}, "cornerRadius": {"$ref": "#/refs/numberValue"}, "cornerRadiusTopLeft": {"$ref": "#/refs/numberValue"}, "cornerRadiusTopRight": {"$ref": "#/refs/numberValue"}, "cornerRadiusBottomRight": {"$ref": "#/refs/numberValue"}, "cornerRadiusBottomLeft": {"$ref": "#/refs/numberValue"}, "angle": {"$ref": "#/refs/numberValue"}, "size": {"$ref": "#/refs/numberValue"}, "shape": {"$ref": "#/refs/stringValue"}, "path": {"$ref": "#/refs/stringValue"}, "scaleX": {"$ref": "#/refs/numberValue"}, "scaleY": {"$ref": "#/refs/numberValue"}, "innerRadius": {"$ref": "#/refs/numberValue"}, "outerRadius": {"$ref": "#/refs/numberValue"}, "startAngle": {"$ref": "#/refs/numberValue"}, "endAngle": {"$ref": "#/refs/numberValue"}, "padAngle": {"$ref": "#/refs/numberValue"}, "interpolate": {"$ref": "#/refs/stringValue"}, "tension": {"$ref": "#/refs/numberValue"}, "orient": {"$ref": "#/refs/directionValue"}, "defined": {"$ref": "#/refs/booleanValue"}, "url": {"$ref": "#/refs/stringValue"}, "align": {"$ref": "#/refs/alignValue"}, "baseline": {"$ref": "#/refs/baselineValue"}, "aspect": {"$ref": "#/refs/booleanValue"}, "smooth": {"$ref": "#/refs/booleanValue"}, "text": {"$ref": "#/refs/textValue"}, "dir": {"$ref": "#/refs/stringValue"}, "ellipsis": {"$ref": "#/refs/stringValue"}, "limit": {"$ref": "#/refs/numberValue"}, "lineBreak": {"$ref": "#/refs/stringValue"}, "lineHeight": {"$ref": "#/refs/numberValue"}, "dx": {"$ref": "#/refs/numberValue"}, "dy": {"$ref": "#/refs/numberValue"}, "radius": {"$ref": "#/refs/numberValue"}, "theta": {"$ref": "#/refs/numberValue"}, "font": {"$ref": "#/refs/stringValue"}, "fontSize": {"$ref": "#/refs/numberValue"}, "fontWeight": {"$ref": "#/refs/fontWeightValue"}, "fontStyle": {"$ref": "#/refs/stringValue"}}, "additionalProperties": true}, "encode": {"type": "object", "additionalProperties": false, "patternProperties": {"^.+$": {"$ref": "#/defs/encodeEntry"}}}, "layout": {"oneOf": [{"type": "object", "properties": {"align": {"oneOf": [{"oneOf": [{"enum": ["all", "each", "none"]}, {"$ref": "#/refs/signal"}]}, {"type": "object", "properties": {"row": {"oneOf": [{"enum": ["all", "each", "none"]}, {"$ref": "#/refs/signal"}]}, "column": {"oneOf": [{"enum": ["all", "each", "none"]}, {"$ref": "#/refs/signal"}]}}, "additionalProperties": false}]}, "bounds": {"oneOf": [{"enum": ["full", "flush"]}, {"$ref": "#/refs/signal"}]}, "center": {"oneOf": [{"type": "boolean"}, {"$ref": "#/refs/signal"}, {"type": "object", "properties": {"row": {"$ref": "#/refs/booleanOrSignal"}, "column": {"$ref": "#/refs/booleanOrSignal"}}, "additionalProperties": false}]}, "columns": {"$ref": "#/refs/numberOrSignal"}, "padding": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/signal"}, {"type": "object", "properties": {"row": {"$ref": "#/refs/numberOrSignal"}, "column": {"$ref": "#/refs/numberOrSignal"}}, "additionalProperties": false}]}, "offset": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/signal"}, {"type": "object", "properties": {"rowHeader": {"$ref": "#/refs/numberOrSignal"}, "rowFooter": {"$ref": "#/refs/numberOrSignal"}, "rowTitle": {"$ref": "#/refs/numberOrSignal"}, "columnHeader": {"$ref": "#/refs/numberOrSignal"}, "columnFooter": {"$ref": "#/refs/numberOrSignal"}, "columnTitle": {"$ref": "#/refs/numberOrSignal"}}, "additionalProperties": false}]}, "headerBand": {"oneOf": [{"$ref": "#/refs/numberOrSignal"}, {"type": "null"}, {"type": "object", "properties": {"row": {"$ref": "#/refs/numberOrSignal"}, "column": {"$ref": "#/refs/numberOrSignal"}}, "additionalProperties": false}]}, "footerBand": {"oneOf": [{"$ref": "#/refs/numberOrSignal"}, {"type": "null"}, {"type": "object", "properties": {"row": {"$ref": "#/refs/numberOrSignal"}, "column": {"$ref": "#/refs/numberOrSignal"}}, "additionalProperties": false}]}, "titleBand": {"oneOf": [{"$ref": "#/refs/numberOrSignal"}, {"type": "null"}, {"type": "object", "properties": {"row": {"$ref": "#/refs/numberOrSignal"}, "column": {"$ref": "#/refs/numberOrSignal"}}, "additionalProperties": false}]}, "titleAnchor": {"oneOf": [{"oneOf": [{"enum": ["start", "end"]}, {"$ref": "#/refs/signal"}]}, {"type": "object", "properties": {"row": {"oneOf": [{"enum": ["start", "end"]}, {"$ref": "#/refs/signal"}]}, "column": {"oneOf": [{"enum": ["start", "end"]}, {"$ref": "#/refs/signal"}]}}, "additionalProperties": false}]}}, "additionalProperties": false}, {"$ref": "#/refs/signal"}]}, "guideEncode": {"type": "object", "properties": {"name": {"type": "string"}, "interactive": {"type": "boolean", "default": false}, "style": {"$ref": "#/refs/style"}}, "additionalProperties": false, "patternProperties": {"^(?!interactive|name|style).+$": {"$ref": "#/defs/encodeEntry"}}}, "legend": {"allOf": [{"type": "object", "properties": {"size": {"type": "string"}, "shape": {"type": "string"}, "fill": {"type": "string"}, "stroke": {"type": "string"}, "opacity": {"type": "string"}, "strokeDash": {"type": "string"}, "strokeWidth": {"type": "string"}, "type": {"enum": ["gradient", "symbol"]}, "direction": {"enum": ["vertical", "horizontal"]}, "orient": {"oneOf": [{"enum": ["none", "left", "right", "top", "bottom", "top-left", "top-right", "bottom-left", "bottom-right"], "default": "right"}, {"$ref": "#/refs/signal"}]}, "tickCount": {"$ref": "#/refs/tickCount"}, "tickMinStep": {"$ref": "#/refs/numberOrSignal"}, "symbolLimit": {"$ref": "#/refs/numberOrSignal"}, "values": {"$ref": "#/refs/arrayOrSignal"}, "zindex": {"type": "number"}, "cornerRadius": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "fillColor": {"oneOf": [{"type": "null"}, {"type": "string"}, {"$ref": "#/refs/colorValue"}]}, "offset": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "padding": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "strokeColor": {"oneOf": [{"type": "null"}, {"type": "string"}, {"$ref": "#/refs/colorValue"}]}, "legendX": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "legendY": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "title": {"$ref": "#/refs/textOrSignal"}, "titleAlign": {"oneOf": [{"enum": ["left", "right", "center"]}, {"$ref": "#/refs/alignValue"}]}, "titleAnchor": {"oneOf": [{"enum": [null, "start", "middle", "end"]}, {"$ref": "#/refs/anchorValue"}]}, "titleBaseline": {"oneOf": [{"enum": ["top", "middle", "bottom", "alphabetic", "line-top", "line-bottom"]}, {"$ref": "#/refs/baselineValue"}]}, "titleColor": {"oneOf": [{"type": "null"}, {"type": "string"}, {"$ref": "#/refs/colorValue"}]}, "titleFont": {"oneOf": [{"type": "string"}, {"$ref": "#/refs/stringValue"}]}, "titleFontSize": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "titleFontStyle": {"oneOf": [{"type": "string"}, {"$ref": "#/refs/stringValue"}]}, "titleFontWeight": {"oneOf": [{"enum": [null, "normal", "bold", "lighter", "bolder", "100", "200", "300", "400", "500", "600", "700", "800", "900", 100, 200, 300, 400, 500, 600, 700, 800, 900]}, {"$ref": "#/refs/fontWeightValue"}]}, "titleLimit": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "titleLineHeight": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "titleOpacity": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "titleOrient": {"oneOf": [{"enum": ["left", "right", "top", "bottom"]}, {"$ref": "#/refs/orientValue"}]}, "titlePadding": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "gradientLength": {"$ref": "#/refs/numberOrSignal"}, "gradientOpacity": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "gradientStrokeColor": {"oneOf": [{"type": "null"}, {"type": "string"}, {"$ref": "#/refs/colorValue"}]}, "gradientStrokeWidth": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "gradientThickness": {"$ref": "#/refs/numberOrSignal"}, "clipHeight": {"$ref": "#/refs/numberOrSignal"}, "columns": {"$ref": "#/refs/numberOrSignal"}, "columnPadding": {"$ref": "#/refs/numberOrSignal"}, "rowPadding": {"$ref": "#/refs/numberOrSignal"}, "gridAlign": {"oneOf": [{"enum": ["all", "each", "none"]}, {"$ref": "#/refs/signal"}]}, "symbolDash": {"oneOf": [{"type": "array", "items": {"type": "number"}}, {"$ref": "#/refs/arrayValue"}]}, "symbolDashOffset": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "symbolFillColor": {"oneOf": [{"type": "null"}, {"type": "string"}, {"$ref": "#/refs/colorValue"}]}, "symbolOffset": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "symbolOpacity": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "symbolSize": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "symbolStrokeColor": {"oneOf": [{"type": "null"}, {"type": "string"}, {"$ref": "#/refs/colorValue"}]}, "symbolStrokeWidth": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "symbolType": {"oneOf": [{"type": "string"}, {"$ref": "#/refs/stringValue"}]}, "format": {"oneOf": [{"type": "string"}, {"type": "object", "properties": {"year": {"type": "string"}, "quarter": {"type": "string"}, "month": {"type": "string"}, "date": {"type": "string"}, "week": {"type": "string"}, "day": {"type": "string"}, "hours": {"type": "string"}, "minutes": {"type": "string"}, "seconds": {"type": "string"}, "milliseconds": {"type": "string"}}, "additionalProperties": false}, {"$ref": "#/refs/signal"}]}, "formatType": {"oneOf": [{"enum": ["number", "time", "utc"]}, {"$ref": "#/refs/signal"}]}, "labelAlign": {"oneOf": [{"enum": ["left", "right", "center"]}, {"$ref": "#/refs/alignValue"}]}, "labelBaseline": {"oneOf": [{"enum": ["top", "middle", "bottom", "alphabetic", "line-top", "line-bottom"]}, {"$ref": "#/refs/baselineValue"}]}, "labelColor": {"oneOf": [{"type": "null"}, {"type": "string"}, {"$ref": "#/refs/colorValue"}]}, "labelFont": {"oneOf": [{"type": "string"}, {"$ref": "#/refs/stringValue"}]}, "labelFontSize": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "labelFontStyle": {"oneOf": [{"type": "string"}, {"$ref": "#/refs/stringValue"}]}, "labelFontWeight": {"oneOf": [{"enum": [null, "normal", "bold", "lighter", "bolder", "100", "200", "300", "400", "500", "600", "700", "800", "900", 100, 200, 300, 400, 500, 600, 700, 800, 900]}, {"$ref": "#/refs/fontWeightValue"}]}, "labelLimit": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "labelOffset": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "labelOpacity": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "labelOverlap": {"$ref": "#/refs/labelOverlap"}, "labelSeparation": {"$ref": "#/refs/numberOrSignal"}, "encode": {"type": "object", "properties": {"title": {"$ref": "#/defs/guideEncode"}, "labels": {"$ref": "#/defs/guideEncode"}, "legend": {"$ref": "#/defs/guideEncode"}, "entries": {"$ref": "#/defs/guideEncode"}, "symbols": {"$ref": "#/defs/guideEncode"}, "gradient": {"$ref": "#/defs/guideEncode"}}, "additionalProperties": false}}, "additionalProperties": false}, {"anyOf": [{"type": "object", "required": ["size"]}, {"type": "object", "required": ["shape"]}, {"type": "object", "required": ["fill"]}, {"type": "object", "required": ["stroke"]}, {"type": "object", "required": ["opacity"]}, {"type": "object", "required": ["strokeDash"]}, {"type": "object", "required": ["strokeWidth"]}]}]}, "mark": {"type": "object", "properties": {"type": {"$ref": "#/refs/marktype"}, "role": {"type": "string"}, "name": {"type": "string"}, "style": {"$ref": "#/refs/style"}, "key": {"type": "string"}, "clip": {"$ref": "#/refs/markclip"}, "sort": {"$ref": "#/refs/compare"}, "interactive": {"$ref": "#/refs/booleanOrSignal"}, "encode": {"$ref": "#/defs/encode"}, "transform": {"type": "array", "items": {"$ref": "#/defs/transformMark"}}, "on": {"$ref": "#/defs/onMarkTrigger"}}, "required": ["type"]}, "markGroup": {"allOf": [{"type": "object", "properties": {"type": {"enum": ["group"]}, "from": {"oneOf": [{"$ref": "#/refs/from"}, {"$ref": "#/refs/facet"}]}}, "required": ["type"]}, {"$ref": "#/defs/mark"}, {"$ref": "#/defs/scope"}]}, "markVisual": {"allOf": [{"type": "object", "properties": {"type": {"not": {"enum": ["group"]}}, "from": {"$ref": "#/refs/from"}}}, {"$ref": "#/defs/mark"}]}, "listener": {"oneOf": [{"$ref": "#/refs/signal"}, {"type": "object", "properties": {"scale": {"type": "string"}}, "required": ["scale"]}, {"$ref": "#/defs/stream"}]}, "onEvents": {"type": "array", "items": {"allOf": [{"type": "object", "properties": {"events": {"oneOf": [{"$ref": "#/refs/selector"}, {"$ref": "#/defs/listener"}, {"type": "array", "items": {"$ref": "#/defs/listener"}, "minItems": 1}]}, "force": {"type": "boolean"}}, "required": ["events"]}, {"oneOf": [{"type": "object", "properties": {"encode": {"type": "string"}}, "required": ["encode"]}, {"type": "object", "properties": {"update": {"oneOf": [{"$ref": "#/refs/exprString"}, {"$ref": "#/refs/expr"}, {"$ref": "#/refs/signal"}, {"type": "object", "properties": {"value": {}}, "required": ["value"]}]}}, "required": ["update"]}]}]}}, "onTrigger": {"type": "array", "items": {"type": "object", "properties": {"trigger": {"$ref": "#/refs/exprString"}, "insert": {"$ref": "#/refs/exprString"}, "remove": {"oneOf": [{"type": "boolean"}, {"$ref": "#/refs/exprString"}]}, "toggle": {"$ref": "#/refs/exprString"}, "modify": {"$ref": "#/refs/exprString"}, "values": {"$ref": "#/refs/exprString"}}, "required": ["trigger"], "additionalProperties": false}}, "onMarkTrigger": {"type": "array", "items": {"type": "object", "properties": {"trigger": {"$ref": "#/refs/exprString"}, "modify": {"$ref": "#/refs/exprString"}, "values": {"$ref": "#/refs/exprString"}}, "required": ["trigger"], "additionalProperties": false}}, "padding": {"oneOf": [{"type": "number"}, {"type": "object", "properties": {"top": {"type": "number"}, "bottom": {"type": "number"}, "left": {"type": "number"}, "right": {"type": "number"}}, "additionalProperties": false}, {"$ref": "#/refs/signal"}]}, "projection": {"type": "object", "properties": {"name": {"type": "string"}, "type": {"$ref": "#/refs/stringOrSignal"}, "clipAngle": {"$ref": "#/refs/numberOrSignal"}, "clipExtent": {"oneOf": [{"type": "array", "items": {"oneOf": [{"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}, "minItems": 2, "maxItems": 2}, {"$ref": "#/refs/signal"}]}, "minItems": 2, "maxItems": 2}, {"$ref": "#/refs/signal"}]}, "scale": {"$ref": "#/refs/numberOrSignal"}, "translate": {"oneOf": [{"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}, "minItems": 2, "maxItems": 2}, {"$ref": "#/refs/signal"}]}, "center": {"oneOf": [{"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}, "minItems": 2, "maxItems": 2}, {"$ref": "#/refs/signal"}]}, "rotate": {"oneOf": [{"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}, "minItems": 2, "maxItems": 3}, {"$ref": "#/refs/signal"}]}, "parallels": {"oneOf": [{"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}, "minItems": 2, "maxItems": 2}, {"$ref": "#/refs/signal"}]}, "precision": {"$ref": "#/refs/numberOrSignal"}, "pointRadius": {"$ref": "#/refs/numberOrSignal"}, "fit": {"oneOf": [{"type": "object"}, {"type": "array"}]}, "extent": {"oneOf": [{"type": "array", "items": {"oneOf": [{"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}, "minItems": 2, "maxItems": 2}, {"$ref": "#/refs/signal"}]}, "minItems": 2, "maxItems": 2}, {"$ref": "#/refs/signal"}]}, "size": {"oneOf": [{"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}, "minItems": 2, "maxItems": 2}, {"$ref": "#/refs/signal"}]}}, "required": ["name"], "additionalProperties": true}, "scale": {"oneOf": [{"type": "object", "properties": {"type": {"enum": ["identity"]}, "nice": {"$ref": "#/refs/booleanOrSignal"}, "name": {"type": "string"}, "domain": {"oneOf": [{"type": "array", "items": {"oneOf": [{"type": "null"}, {"type": "boolean"}, {"type": "string"}, {"type": "number"}, {"$ref": "#/refs/signal"}, {"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}}]}}, {"$ref": "#/refs/scaleData"}, {"$ref": "#/refs/signal"}]}, "domainMin": {"$ref": "#/refs/numberOrSignal"}, "domainMax": {"$ref": "#/refs/numberOrSignal"}, "domainMid": {"$ref": "#/refs/numberOrSignal"}, "domainRaw": {"oneOf": [{"type": "null"}, {"type": "array"}, {"$ref": "#/refs/signal"}]}, "reverse": {"$ref": "#/refs/booleanOrSignal"}, "round": {"$ref": "#/refs/booleanOrSignal"}}, "required": ["type", "name"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"enum": ["ordinal"]}, "range": {"oneOf": [{"enum": ["width", "height", "symbol", "category", "ordinal", "ramp", "diverging", "heatmap"]}, {"type": "array", "items": {"oneOf": [{"type": "null"}, {"type": "boolean"}, {"type": "string"}, {"type": "number"}, {"$ref": "#/refs/signal"}, {"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}}]}}, {"type": "object", "properties": {"scheme": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"oneOf": [{"type": "string"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}]}, "count": {"$ref": "#/refs/numberOrSignal"}, "extent": {"oneOf": [{"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}, "numItems": 2}, {"$ref": "#/refs/signal"}]}}, "required": ["scheme"], "additionalProperties": false}, {"oneOf": [{"type": "object", "properties": {"data": {"type": "string"}, "field": {"$ref": "#/refs/stringOrSignal"}, "sort": {"oneOf": [{"type": "boolean"}, {"type": "object", "properties": {"field": {"$ref": "#/refs/stringOrSignal"}, "op": {"$ref": "#/refs/stringOrSignal"}, "order": {"$ref": "#/refs/sortOrder"}}, "additionalProperties": false}]}}, "required": ["data", "field"], "additionalProperties": false}, {"type": "object", "properties": {"data": {"type": "string"}, "fields": {"type": "array", "items": {"$ref": "#/refs/stringOrSignal"}, "minItems": 1}, "sort": {"oneOf": [{"type": "boolean"}, {"type": "object", "properties": {"op": {"enum": ["count"]}, "order": {"$ref": "#/refs/sortOrder"}}, "additionalProperties": false}, {"type": "object", "properties": {"field": {"$ref": "#/refs/stringOrSignal"}, "op": {"enum": ["count", "min", "max"]}, "order": {"$ref": "#/refs/sortOrder"}}, "required": ["field", "op"], "additionalProperties": false}]}}, "required": ["data", "fields"], "additionalProperties": false}, {"type": "object", "properties": {"fields": {"type": "array", "items": {"oneOf": [{"type": "object", "properties": {"data": {"type": "string"}, "field": {"$ref": "#/refs/stringOrSignal"}}, "required": ["data", "field"], "additionalProperties": false}, {"type": "array", "items": {"oneOf": [{"type": "string"}, {"type": "number"}, {"type": "boolean"}]}}, {"$ref": "#/refs/signal"}]}, "minItems": 1}, "sort": {"oneOf": [{"type": "boolean"}, {"type": "object", "properties": {"op": {"enum": ["count"]}, "order": {"$ref": "#/refs/sortOrder"}}, "additionalProperties": false}, {"type": "object", "properties": {"field": {"$ref": "#/refs/stringOrSignal"}, "op": {"enum": ["count", "min", "max"]}, "order": {"$ref": "#/refs/sortOrder"}}, "required": ["field", "op"], "additionalProperties": false}]}}, "required": ["fields"], "additionalProperties": false}]}, {"$ref": "#/refs/signal"}]}, "interpolate": {"$ref": "#/refs/scaleInterpolate"}, "domainImplicit": {"$ref": "#/refs/booleanOrSignal"}, "name": {"type": "string"}, "domain": {"oneOf": [{"type": "array", "items": {"oneOf": [{"type": "null"}, {"type": "boolean"}, {"type": "string"}, {"type": "number"}, {"$ref": "#/refs/signal"}, {"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}}]}}, {"$ref": "#/refs/scaleData"}, {"$ref": "#/refs/signal"}]}, "domainMin": {"$ref": "#/refs/numberOrSignal"}, "domainMax": {"$ref": "#/refs/numberOrSignal"}, "domainMid": {"$ref": "#/refs/numberOrSignal"}, "domainRaw": {"oneOf": [{"type": "null"}, {"type": "array"}, {"$ref": "#/refs/signal"}]}, "reverse": {"$ref": "#/refs/booleanOrSignal"}, "round": {"$ref": "#/refs/booleanOrSignal"}}, "required": ["type", "name"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"enum": ["band"]}, "paddingInner": {"$ref": "#/refs/numberOrSignal"}, "range": {"oneOf": [{"enum": ["width", "height", "symbol", "category", "ordinal", "ramp", "diverging", "heatmap"]}, {"type": "array", "items": {"oneOf": [{"type": "null"}, {"type": "boolean"}, {"type": "string"}, {"type": "number"}, {"$ref": "#/refs/signal"}, {"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}}]}}, {"type": "object", "properties": {"step": {"$ref": "#/refs/numberOrSignal"}}, "required": ["step"], "additionalProperties": false}, {"$ref": "#/refs/signal"}]}, "padding": {"$ref": "#/refs/numberOrSignal"}, "paddingOuter": {"$ref": "#/refs/numberOrSignal"}, "align": {"$ref": "#/refs/numberOrSignal"}, "name": {"type": "string"}, "domain": {"oneOf": [{"type": "array", "items": {"oneOf": [{"type": "null"}, {"type": "boolean"}, {"type": "string"}, {"type": "number"}, {"$ref": "#/refs/signal"}, {"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}}]}}, {"$ref": "#/refs/scaleData"}, {"$ref": "#/refs/signal"}]}, "domainMin": {"$ref": "#/refs/numberOrSignal"}, "domainMax": {"$ref": "#/refs/numberOrSignal"}, "domainMid": {"$ref": "#/refs/numberOrSignal"}, "domainRaw": {"oneOf": [{"type": "null"}, {"type": "array"}, {"$ref": "#/refs/signal"}]}, "reverse": {"$ref": "#/refs/booleanOrSignal"}, "round": {"$ref": "#/refs/booleanOrSignal"}}, "required": ["type", "name"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"enum": ["point"]}, "range": {"oneOf": [{"enum": ["width", "height", "symbol", "category", "ordinal", "ramp", "diverging", "heatmap"]}, {"type": "array", "items": {"oneOf": [{"type": "null"}, {"type": "boolean"}, {"type": "string"}, {"type": "number"}, {"$ref": "#/refs/signal"}, {"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}}]}}, {"type": "object", "properties": {"step": {"$ref": "#/refs/numberOrSignal"}}, "required": ["step"], "additionalProperties": false}, {"$ref": "#/refs/signal"}]}, "padding": {"$ref": "#/refs/numberOrSignal"}, "paddingOuter": {"$ref": "#/refs/numberOrSignal"}, "align": {"$ref": "#/refs/numberOrSignal"}, "name": {"type": "string"}, "domain": {"oneOf": [{"type": "array", "items": {"oneOf": [{"type": "null"}, {"type": "boolean"}, {"type": "string"}, {"type": "number"}, {"$ref": "#/refs/signal"}, {"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}}]}}, {"$ref": "#/refs/scaleData"}, {"$ref": "#/refs/signal"}]}, "domainMin": {"$ref": "#/refs/numberOrSignal"}, "domainMax": {"$ref": "#/refs/numberOrSignal"}, "domainMid": {"$ref": "#/refs/numberOrSignal"}, "domainRaw": {"oneOf": [{"type": "null"}, {"type": "array"}, {"$ref": "#/refs/signal"}]}, "reverse": {"$ref": "#/refs/booleanOrSignal"}, "round": {"$ref": "#/refs/booleanOrSignal"}}, "required": ["type", "name"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"enum": ["quantize", "threshold"]}, "range": {"oneOf": [{"enum": ["width", "height", "symbol", "category", "ordinal", "ramp", "diverging", "heatmap"]}, {"type": "array", "items": {"oneOf": [{"type": "null"}, {"type": "boolean"}, {"type": "string"}, {"type": "number"}, {"$ref": "#/refs/signal"}, {"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}}]}}, {"type": "object", "properties": {"scheme": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"oneOf": [{"type": "string"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}]}, "count": {"$ref": "#/refs/numberOrSignal"}, "extent": {"oneOf": [{"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}, "numItems": 2}, {"$ref": "#/refs/signal"}]}}, "required": ["scheme"], "additionalProperties": false}, {"$ref": "#/refs/signal"}]}, "interpolate": {"$ref": "#/refs/scaleInterpolate"}, "nice": {"oneOf": [{"type": "boolean"}, {"type": "number"}, {"$ref": "#/refs/signal"}]}, "zero": {"$ref": "#/refs/booleanOrSignal"}, "name": {"type": "string"}, "domain": {"oneOf": [{"type": "array", "items": {"oneOf": [{"type": "null"}, {"type": "boolean"}, {"type": "string"}, {"type": "number"}, {"$ref": "#/refs/signal"}, {"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}}]}}, {"$ref": "#/refs/scaleData"}, {"$ref": "#/refs/signal"}]}, "domainMin": {"$ref": "#/refs/numberOrSignal"}, "domainMax": {"$ref": "#/refs/numberOrSignal"}, "domainMid": {"$ref": "#/refs/numberOrSignal"}, "domainRaw": {"oneOf": [{"type": "null"}, {"type": "array"}, {"$ref": "#/refs/signal"}]}, "reverse": {"$ref": "#/refs/booleanOrSignal"}, "round": {"$ref": "#/refs/booleanOrSignal"}}, "required": ["type", "name"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"enum": ["quantile"]}, "range": {"oneOf": [{"enum": ["width", "height", "symbol", "category", "ordinal", "ramp", "diverging", "heatmap"]}, {"type": "array", "items": {"oneOf": [{"type": "null"}, {"type": "boolean"}, {"type": "string"}, {"type": "number"}, {"$ref": "#/refs/signal"}, {"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}}]}}, {"type": "object", "properties": {"scheme": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"oneOf": [{"type": "string"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}]}, "count": {"$ref": "#/refs/numberOrSignal"}, "extent": {"oneOf": [{"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}, "numItems": 2}, {"$ref": "#/refs/signal"}]}}, "required": ["scheme"], "additionalProperties": false}, {"$ref": "#/refs/signal"}]}, "interpolate": {"$ref": "#/refs/scaleInterpolate"}, "name": {"type": "string"}, "domain": {"oneOf": [{"type": "array", "items": {"oneOf": [{"type": "null"}, {"type": "boolean"}, {"type": "string"}, {"type": "number"}, {"$ref": "#/refs/signal"}, {"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}}]}}, {"$ref": "#/refs/scaleData"}, {"$ref": "#/refs/signal"}]}, "domainMin": {"$ref": "#/refs/numberOrSignal"}, "domainMax": {"$ref": "#/refs/numberOrSignal"}, "domainMid": {"$ref": "#/refs/numberOrSignal"}, "domainRaw": {"oneOf": [{"type": "null"}, {"type": "array"}, {"$ref": "#/refs/signal"}]}, "reverse": {"$ref": "#/refs/booleanOrSignal"}, "round": {"$ref": "#/refs/booleanOrSignal"}}, "required": ["type", "name"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"enum": ["bin-ordinal"]}, "bins": {"$ref": "#/refs/scaleBins"}, "range": {"oneOf": [{"enum": ["width", "height", "symbol", "category", "ordinal", "ramp", "diverging", "heatmap"]}, {"type": "array", "items": {"oneOf": [{"type": "null"}, {"type": "boolean"}, {"type": "string"}, {"type": "number"}, {"$ref": "#/refs/signal"}, {"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}}]}}, {"type": "object", "properties": {"scheme": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"oneOf": [{"type": "string"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}]}, "count": {"$ref": "#/refs/numberOrSignal"}, "extent": {"oneOf": [{"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}, "numItems": 2}, {"$ref": "#/refs/signal"}]}}, "required": ["scheme"], "additionalProperties": false}, {"$ref": "#/refs/signal"}]}, "interpolate": {"$ref": "#/refs/scaleInterpolate"}, "name": {"type": "string"}, "domain": {"oneOf": [{"type": "array", "items": {"oneOf": [{"type": "null"}, {"type": "boolean"}, {"type": "string"}, {"type": "number"}, {"$ref": "#/refs/signal"}, {"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}}]}}, {"$ref": "#/refs/scaleData"}, {"$ref": "#/refs/signal"}]}, "domainMin": {"$ref": "#/refs/numberOrSignal"}, "domainMax": {"$ref": "#/refs/numberOrSignal"}, "domainMid": {"$ref": "#/refs/numberOrSignal"}, "domainRaw": {"oneOf": [{"type": "null"}, {"type": "array"}, {"$ref": "#/refs/signal"}]}, "reverse": {"$ref": "#/refs/booleanOrSignal"}, "round": {"$ref": "#/refs/booleanOrSignal"}}, "required": ["type", "name"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"enum": ["time", "utc"]}, "nice": {"oneOf": [{"type": "boolean"}, {"enum": ["millisecond", "second", "minute", "hour", "day", "week", "month", "year"]}, {"type": "object", "properties": {"interval": {"oneOf": [{"enum": ["millisecond", "second", "minute", "hour", "day", "week", "month", "year"]}, {"$ref": "#/refs/signal"}]}, "step": {"$ref": "#/refs/numberOrSignal"}}, "required": ["interval"], "additionalProperties": false}]}, "range": {"oneOf": [{"enum": ["width", "height", "symbol", "category", "ordinal", "ramp", "diverging", "heatmap"]}, {"type": "array", "items": {"oneOf": [{"type": "null"}, {"type": "boolean"}, {"type": "string"}, {"type": "number"}, {"$ref": "#/refs/signal"}, {"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}}]}}, {"type": "object", "properties": {"scheme": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"oneOf": [{"type": "string"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}]}, "count": {"$ref": "#/refs/numberOrSignal"}, "extent": {"oneOf": [{"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}, "numItems": 2}, {"$ref": "#/refs/signal"}]}}, "required": ["scheme"], "additionalProperties": false}, {"$ref": "#/refs/signal"}]}, "bins": {"$ref": "#/refs/scaleBins"}, "interpolate": {"$ref": "#/refs/scaleInterpolate"}, "clamp": {"$ref": "#/refs/booleanOrSignal"}, "padding": {"$ref": "#/refs/numberOrSignal"}, "name": {"type": "string"}, "domain": {"oneOf": [{"type": "array", "items": {"oneOf": [{"type": "null"}, {"type": "boolean"}, {"type": "string"}, {"type": "number"}, {"$ref": "#/refs/signal"}, {"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}}]}}, {"$ref": "#/refs/scaleData"}, {"$ref": "#/refs/signal"}]}, "domainMin": {"$ref": "#/refs/numberOrSignal"}, "domainMax": {"$ref": "#/refs/numberOrSignal"}, "domainMid": {"$ref": "#/refs/numberOrSignal"}, "domainRaw": {"oneOf": [{"type": "null"}, {"type": "array"}, {"$ref": "#/refs/signal"}]}, "reverse": {"$ref": "#/refs/booleanOrSignal"}, "round": {"$ref": "#/refs/booleanOrSignal"}}, "required": ["type", "name"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"enum": ["linear", "sqrt", "sequential"]}, "nice": {"oneOf": [{"type": "boolean"}, {"type": "number"}, {"$ref": "#/refs/signal"}]}, "zero": {"$ref": "#/refs/booleanOrSignal"}, "range": {"oneOf": [{"enum": ["width", "height", "symbol", "category", "ordinal", "ramp", "diverging", "heatmap"]}, {"type": "array", "items": {"oneOf": [{"type": "null"}, {"type": "boolean"}, {"type": "string"}, {"type": "number"}, {"$ref": "#/refs/signal"}, {"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}}]}}, {"type": "object", "properties": {"scheme": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"oneOf": [{"type": "string"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}]}, "count": {"$ref": "#/refs/numberOrSignal"}, "extent": {"oneOf": [{"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}, "numItems": 2}, {"$ref": "#/refs/signal"}]}}, "required": ["scheme"], "additionalProperties": false}, {"$ref": "#/refs/signal"}]}, "bins": {"$ref": "#/refs/scaleBins"}, "interpolate": {"$ref": "#/refs/scaleInterpolate"}, "clamp": {"$ref": "#/refs/booleanOrSignal"}, "padding": {"$ref": "#/refs/numberOrSignal"}, "name": {"type": "string"}, "domain": {"oneOf": [{"type": "array", "items": {"oneOf": [{"type": "null"}, {"type": "boolean"}, {"type": "string"}, {"type": "number"}, {"$ref": "#/refs/signal"}, {"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}}]}}, {"$ref": "#/refs/scaleData"}, {"$ref": "#/refs/signal"}]}, "domainMin": {"$ref": "#/refs/numberOrSignal"}, "domainMax": {"$ref": "#/refs/numberOrSignal"}, "domainMid": {"$ref": "#/refs/numberOrSignal"}, "domainRaw": {"oneOf": [{"type": "null"}, {"type": "array"}, {"$ref": "#/refs/signal"}]}, "reverse": {"$ref": "#/refs/booleanOrSignal"}, "round": {"$ref": "#/refs/booleanOrSignal"}}, "required": ["name"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"enum": ["log"]}, "base": {"$ref": "#/refs/numberOrSignal"}, "nice": {"oneOf": [{"type": "boolean"}, {"type": "number"}, {"$ref": "#/refs/signal"}]}, "zero": {"$ref": "#/refs/booleanOrSignal"}, "range": {"oneOf": [{"enum": ["width", "height", "symbol", "category", "ordinal", "ramp", "diverging", "heatmap"]}, {"type": "array", "items": {"oneOf": [{"type": "null"}, {"type": "boolean"}, {"type": "string"}, {"type": "number"}, {"$ref": "#/refs/signal"}, {"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}}]}}, {"type": "object", "properties": {"scheme": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"oneOf": [{"type": "string"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}]}, "count": {"$ref": "#/refs/numberOrSignal"}, "extent": {"oneOf": [{"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}, "numItems": 2}, {"$ref": "#/refs/signal"}]}}, "required": ["scheme"], "additionalProperties": false}, {"$ref": "#/refs/signal"}]}, "bins": {"$ref": "#/refs/scaleBins"}, "interpolate": {"$ref": "#/refs/scaleInterpolate"}, "clamp": {"$ref": "#/refs/booleanOrSignal"}, "padding": {"$ref": "#/refs/numberOrSignal"}, "name": {"type": "string"}, "domain": {"oneOf": [{"type": "array", "items": {"oneOf": [{"type": "null"}, {"type": "boolean"}, {"type": "string"}, {"type": "number"}, {"$ref": "#/refs/signal"}, {"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}}]}}, {"$ref": "#/refs/scaleData"}, {"$ref": "#/refs/signal"}]}, "domainMin": {"$ref": "#/refs/numberOrSignal"}, "domainMax": {"$ref": "#/refs/numberOrSignal"}, "domainMid": {"$ref": "#/refs/numberOrSignal"}, "domainRaw": {"oneOf": [{"type": "null"}, {"type": "array"}, {"$ref": "#/refs/signal"}]}, "reverse": {"$ref": "#/refs/booleanOrSignal"}, "round": {"$ref": "#/refs/booleanOrSignal"}}, "required": ["type", "name"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"enum": ["pow"]}, "exponent": {"$ref": "#/refs/numberOrSignal"}, "nice": {"oneOf": [{"type": "boolean"}, {"type": "number"}, {"$ref": "#/refs/signal"}]}, "zero": {"$ref": "#/refs/booleanOrSignal"}, "range": {"oneOf": [{"enum": ["width", "height", "symbol", "category", "ordinal", "ramp", "diverging", "heatmap"]}, {"type": "array", "items": {"oneOf": [{"type": "null"}, {"type": "boolean"}, {"type": "string"}, {"type": "number"}, {"$ref": "#/refs/signal"}, {"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}}]}}, {"type": "object", "properties": {"scheme": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"oneOf": [{"type": "string"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}]}, "count": {"$ref": "#/refs/numberOrSignal"}, "extent": {"oneOf": [{"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}, "numItems": 2}, {"$ref": "#/refs/signal"}]}}, "required": ["scheme"], "additionalProperties": false}, {"$ref": "#/refs/signal"}]}, "bins": {"$ref": "#/refs/scaleBins"}, "interpolate": {"$ref": "#/refs/scaleInterpolate"}, "clamp": {"$ref": "#/refs/booleanOrSignal"}, "padding": {"$ref": "#/refs/numberOrSignal"}, "name": {"type": "string"}, "domain": {"oneOf": [{"type": "array", "items": {"oneOf": [{"type": "null"}, {"type": "boolean"}, {"type": "string"}, {"type": "number"}, {"$ref": "#/refs/signal"}, {"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}}]}}, {"$ref": "#/refs/scaleData"}, {"$ref": "#/refs/signal"}]}, "domainMin": {"$ref": "#/refs/numberOrSignal"}, "domainMax": {"$ref": "#/refs/numberOrSignal"}, "domainMid": {"$ref": "#/refs/numberOrSignal"}, "domainRaw": {"oneOf": [{"type": "null"}, {"type": "array"}, {"$ref": "#/refs/signal"}]}, "reverse": {"$ref": "#/refs/booleanOrSignal"}, "round": {"$ref": "#/refs/booleanOrSignal"}}, "required": ["type", "name"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"enum": ["symlog"]}, "constant": {"$ref": "#/refs/numberOrSignal"}, "nice": {"oneOf": [{"type": "boolean"}, {"type": "number"}, {"$ref": "#/refs/signal"}]}, "zero": {"$ref": "#/refs/booleanOrSignal"}, "range": {"oneOf": [{"enum": ["width", "height", "symbol", "category", "ordinal", "ramp", "diverging", "heatmap"]}, {"type": "array", "items": {"oneOf": [{"type": "null"}, {"type": "boolean"}, {"type": "string"}, {"type": "number"}, {"$ref": "#/refs/signal"}, {"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}}]}}, {"type": "object", "properties": {"scheme": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"oneOf": [{"type": "string"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}]}, "count": {"$ref": "#/refs/numberOrSignal"}, "extent": {"oneOf": [{"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}, "numItems": 2}, {"$ref": "#/refs/signal"}]}}, "required": ["scheme"], "additionalProperties": false}, {"$ref": "#/refs/signal"}]}, "bins": {"$ref": "#/refs/scaleBins"}, "interpolate": {"$ref": "#/refs/scaleInterpolate"}, "clamp": {"$ref": "#/refs/booleanOrSignal"}, "padding": {"$ref": "#/refs/numberOrSignal"}, "name": {"type": "string"}, "domain": {"oneOf": [{"type": "array", "items": {"oneOf": [{"type": "null"}, {"type": "boolean"}, {"type": "string"}, {"type": "number"}, {"$ref": "#/refs/signal"}, {"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}}]}}, {"$ref": "#/refs/scaleData"}, {"$ref": "#/refs/signal"}]}, "domainMin": {"$ref": "#/refs/numberOrSignal"}, "domainMax": {"$ref": "#/refs/numberOrSignal"}, "domainMid": {"$ref": "#/refs/numberOrSignal"}, "domainRaw": {"oneOf": [{"type": "null"}, {"type": "array"}, {"$ref": "#/refs/signal"}]}, "reverse": {"$ref": "#/refs/booleanOrSignal"}, "round": {"$ref": "#/refs/booleanOrSignal"}}, "required": ["type", "name"], "additionalProperties": false}]}, "scope": {"type": "object", "properties": {"encode": {"$ref": "#/defs/encode"}, "layout": {"$ref": "#/defs/layout"}, "signals": {"type": "array", "items": {"$ref": "#/defs/signal"}}, "data": {"type": "array", "items": {"$ref": "#/defs/data"}}, "scales": {"type": "array", "items": {"$ref": "#/defs/scale"}}, "projections": {"type": "array", "items": {"$ref": "#/defs/projection"}}, "axes": {"type": "array", "items": {"$ref": "#/defs/axis"}}, "legends": {"type": "array", "items": {"$ref": "#/defs/legend"}}, "title": {"$ref": "#/defs/title"}, "marks": {"type": "array", "items": {"oneOf": [{"$ref": "#/defs/markGroup"}, {"$ref": "#/defs/markVisual"}]}}, "usermeta": {"type": "object"}}}, "signalName": {"type": "string", "not": {"enum": ["parent", "datum", "event", "item"]}}, "signal": {"oneOf": [{"type": "object", "properties": {"name": {"$ref": "#/defs/signalName"}, "description": {"type": "string"}, "push": {"enum": ["outer"]}, "on": {"$ref": "#/defs/onEvents"}}, "required": ["name", "push"], "additionalProperties": false}, {"type": "object", "properties": {"name": {"$ref": "#/defs/signalName"}, "description": {"type": "string"}, "value": {}, "react": {"type": "boolean", "default": true}, "update": {"$ref": "#/refs/exprString"}, "on": {"$ref": "#/defs/onEvents"}, "bind": {"$ref": "#/defs/bind"}}, "required": ["name"], "additionalProperties": false}, {"type": "object", "properties": {"name": {"$ref": "#/defs/signalName"}, "description": {"type": "string"}, "value": {}, "init": {"$ref": "#/refs/exprString"}, "on": {"$ref": "#/defs/onEvents"}, "bind": {"$ref": "#/defs/bind"}}, "required": ["name", "init"], "additionalProperties": false}]}, "stream": {"allOf": [{"type": "object", "properties": {"between": {"type": "array", "items": {"$ref": "#/defs/stream"}, "minItems": 2, "maxItems": 2}, "marktype": {"type": "string"}, "markname": {"type": "string"}, "filter": {"oneOf": [{"$ref": "#/refs/exprString"}, {"type": "array", "items": {"$ref": "#/refs/exprString"}, "minItems": 1}]}, "throttle": {"type": "number"}, "debounce": {"type": "number"}, "consume": {"type": "boolean"}}}, {"oneOf": [{"type": "object", "properties": {"type": {"type": "string"}, "source": {"type": "string"}}, "required": ["type"]}, {"type": "object", "properties": {"stream": {"$ref": "#/defs/stream"}}, "required": ["stream"]}, {"type": "object", "properties": {"merge": {"type": "array", "items": {"$ref": "#/defs/stream"}, "minItems": 1}}, "required": ["merge"]}]}]}, "title": {"oneOf": [{"type": "string"}, {"type": "object", "properties": {"orient": {"oneOf": [{"enum": ["none", "left", "right", "top", "bottom"], "default": "top"}, {"$ref": "#/refs/signal"}]}, "anchor": {"oneOf": [{"enum": [null, "start", "middle", "end"]}, {"$ref": "#/refs/anchorValue"}]}, "frame": {"oneOf": [{"enum": ["group", "bounds"]}, {"$ref": "#/refs/stringValue"}]}, "offset": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "limit": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "text": {"$ref": "#/refs/textOrSignal"}, "subtitle": {"$ref": "#/refs/textOrSignal"}, "zindex": {"type": "number"}, "align": {"oneOf": [{"enum": ["left", "right", "center"]}, {"$ref": "#/refs/alignValue"}]}, "angle": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "baseline": {"oneOf": [{"enum": ["top", "middle", "bottom", "alphabetic", "line-top", "line-bottom"]}, {"$ref": "#/refs/baselineValue"}]}, "dx": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "dy": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "color": {"oneOf": [{"type": "null"}, {"type": "string"}, {"$ref": "#/refs/colorValue"}]}, "font": {"oneOf": [{"type": "string"}, {"$ref": "#/refs/stringValue"}]}, "fontSize": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "fontStyle": {"oneOf": [{"type": "string"}, {"$ref": "#/refs/stringValue"}]}, "fontWeight": {"oneOf": [{"enum": [null, "normal", "bold", "lighter", "bolder", "100", "200", "300", "400", "500", "600", "700", "800", "900", 100, 200, 300, 400, 500, 600, 700, 800, 900]}, {"$ref": "#/refs/fontWeightValue"}]}, "lineHeight": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "subtitleColor": {"oneOf": [{"type": "null"}, {"type": "string"}, {"$ref": "#/refs/colorValue"}]}, "subtitleFont": {"oneOf": [{"type": "string"}, {"$ref": "#/refs/stringValue"}]}, "subtitleFontSize": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "subtitleFontStyle": {"oneOf": [{"type": "string"}, {"$ref": "#/refs/stringValue"}]}, "subtitleFontWeight": {"oneOf": [{"enum": [null, "normal", "bold", "lighter", "bolder", "100", "200", "300", "400", "500", "600", "700", "800", "900", 100, 200, 300, 400, 500, 600, 700, 800, 900]}, {"$ref": "#/refs/fontWeightValue"}]}, "subtitleLineHeight": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "subtitlePadding": {"$ref": "#/refs/numberOrSignal"}, "encode": {"anyOf": [{"type": "object", "additionalProperties": false, "patternProperties": {"^(?!interactive|name|style).+$": {"$ref": "#/defs/encodeEntry"}}}, {"type": "object", "properties": {"group": {"$ref": "#/defs/guideEncode"}, "title": {"$ref": "#/defs/guideEncode"}, "subtitle": {"$ref": "#/defs/guideEncode"}}, "additionalProperties": false}]}, "name": {"type": "string"}, "interactive": {"type": "boolean"}, "style": {"$ref": "#/refs/style"}}, "additionalProperties": false}]}, "transform": {"oneOf": [{"$ref": "#/defs/crossfilterTransform"}, {"$ref": "#/defs/resolvefilterTransform"}, {"$ref": "#/defs/linkpathTransform"}, {"$ref": "#/defs/pieTransform"}, {"$ref": "#/defs/stackTransform"}, {"$ref": "#/defs/forceTransform"}, {"$ref": "#/defs/contourTransform"}, {"$ref": "#/defs/geojsonTransform"}, {"$ref": "#/defs/geopathTransform"}, {"$ref": "#/defs/geopointTransform"}, {"$ref": "#/defs/geoshapeTransform"}, {"$ref": "#/defs/graticuleTransform"}, {"$ref": "#/defs/heatmapTransform"}, {"$ref": "#/defs/isocontourTransform"}, {"$ref": "#/defs/kde2dTransform"}, {"$ref": "#/defs/nestTransform"}, {"$ref": "#/defs/packTransform"}, {"$ref": "#/defs/partitionTransform"}, {"$ref": "#/defs/stratifyTransform"}, {"$ref": "#/defs/treeTransform"}, {"$ref": "#/defs/treelinksTransform"}, {"$ref": "#/defs/treemapTransform"}, {"$ref": "#/defs/loessTransform"}, {"$ref": "#/defs/regressionTransform"}, {"$ref": "#/defs/aggregateTransform"}, {"$ref": "#/defs/binTransform"}, {"$ref": "#/defs/collectTransform"}, {"$ref": "#/defs/countpatternTransform"}, {"$ref": "#/defs/crossTransform"}, {"$ref": "#/defs/densityTransform"}, {"$ref": "#/defs/dotbinTransform"}, {"$ref": "#/defs/extentTransform"}, {"$ref": "#/defs/filterTransform"}, {"$ref": "#/defs/flattenTransform"}, {"$ref": "#/defs/foldTransform"}, {"$ref": "#/defs/formulaTransform"}, {"$ref": "#/defs/imputeTransform"}, {"$ref": "#/defs/joinaggregateTransform"}, {"$ref": "#/defs/kdeTransform"}, {"$ref": "#/defs/lookupTransform"}, {"$ref": "#/defs/pivotTransform"}, {"$ref": "#/defs/projectTransform"}, {"$ref": "#/defs/quantileTransform"}, {"$ref": "#/defs/sampleTransform"}, {"$ref": "#/defs/sequenceTransform"}, {"$ref": "#/defs/timeunitTransform"}, {"$ref": "#/defs/windowTransform"}, {"$ref": "#/defs/identifierTransform"}, {"$ref": "#/defs/voronoiTransform"}, {"$ref": "#/defs/wordcloudTransform"}]}, "transformMark": {"oneOf": [{"$ref": "#/defs/crossfilterTransform"}, {"$ref": "#/defs/resolvefilterTransform"}, {"$ref": "#/defs/linkpathTransform"}, {"$ref": "#/defs/pieTransform"}, {"$ref": "#/defs/stackTransform"}, {"$ref": "#/defs/forceTransform"}, {"$ref": "#/defs/geojsonTransform"}, {"$ref": "#/defs/geopathTransform"}, {"$ref": "#/defs/geopointTransform"}, {"$ref": "#/defs/geoshapeTransform"}, {"$ref": "#/defs/heatmapTransform"}, {"$ref": "#/defs/packTransform"}, {"$ref": "#/defs/partitionTransform"}, {"$ref": "#/defs/stratifyTransform"}, {"$ref": "#/defs/treeTransform"}, {"$ref": "#/defs/treemapTransform"}, {"$ref": "#/defs/binTransform"}, {"$ref": "#/defs/collectTransform"}, {"$ref": "#/defs/dotbinTransform"}, {"$ref": "#/defs/extentTransform"}, {"$ref": "#/defs/formulaTransform"}, {"$ref": "#/defs/joinaggregateTransform"}, {"$ref": "#/defs/lookupTransform"}, {"$ref": "#/defs/sampleTransform"}, {"$ref": "#/defs/timeunitTransform"}, {"$ref": "#/defs/windowTransform"}, {"$ref": "#/defs/identifierTransform"}, {"$ref": "#/defs/voronoiTransform"}, {"$ref": "#/defs/wordcloudTransform"}]}, "crossfilterTransform": {"type": "object", "properties": {"type": {"enum": ["crossfilter"]}, "signal": {"type": "string"}, "fields": {"oneOf": [{"type": "array", "items": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}}, {"$ref": "#/refs/signal"}]}, "query": {"oneOf": [{"type": "array", "items": {}}, {"$ref": "#/refs/signal"}]}}, "required": ["type", "fields", "query"], "additionalProperties": false}, "resolvefilterTransform": {"type": "object", "properties": {"type": {"enum": ["resolvefilter"]}, "signal": {"type": "string"}, "ignore": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}, "filter": {}}, "required": ["type", "ignore", "filter"], "additionalProperties": false}, "linkpathTransform": {"type": "object", "properties": {"type": {"enum": ["linkpath"]}, "signal": {"type": "string"}, "sourceX": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}], "default": "source.x"}, "sourceY": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}], "default": "source.y"}, "targetX": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}], "default": "target.x"}, "targetY": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}], "default": "target.y"}, "orient": {"anyOf": [{"enum": ["horizontal", "vertical", "radial"]}, {"$ref": "#/refs/signal"}], "default": "vertical"}, "shape": {"anyOf": [{"enum": ["line", "arc", "curve", "diagonal", "orthogonal"]}, {"$ref": "#/refs/signal"}], "default": "line"}, "require": {"$ref": "#/refs/signal"}, "as": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}], "default": "path"}}, "required": ["type"], "additionalProperties": false}, "pieTransform": {"type": "object", "properties": {"type": {"enum": ["pie"]}, "signal": {"type": "string"}, "field": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}, "startAngle": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}, "endAngle": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}], "default": 6.283185307179586}, "sort": {"anyOf": [{"type": "boolean"}, {"$ref": "#/refs/signal"}]}, "as": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}], "maxItems": 2, "minItems": 2, "default": ["startAngle", "endAngle"]}}, "required": ["type"], "additionalProperties": false}, "stackTransform": {"type": "object", "properties": {"type": {"enum": ["stack"]}, "signal": {"type": "string"}, "field": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}, "groupby": {"oneOf": [{"type": "array", "items": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}}, {"$ref": "#/refs/signal"}]}, "sort": {"$ref": "#/refs/compare"}, "offset": {"anyOf": [{"enum": ["zero", "center", "normalize"]}, {"$ref": "#/refs/signal"}], "default": "zero"}, "as": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}], "maxItems": 2, "minItems": 2, "default": ["y0", "y1"]}}, "required": ["type"], "additionalProperties": false}, "forceTransform": {"type": "object", "properties": {"type": {"enum": ["force"]}, "signal": {"type": "string"}, "static": {"anyOf": [{"type": "boolean"}, {"$ref": "#/refs/signal"}]}, "restart": {"anyOf": [{"type": "boolean"}, {"$ref": "#/refs/signal"}]}, "iterations": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}], "default": 300}, "alpha": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}], "default": 1}, "alphaMin": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}], "default": 0.001}, "alphaTarget": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}, "velocityDecay": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}], "default": 0.4}, "forces": {"type": "array", "items": {"oneOf": [{"type": "object", "properties": {"force": {"enum": ["center"]}, "x": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}, "y": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}}, "required": ["force"], "additionalProperties": false}, {"type": "object", "properties": {"force": {"enum": ["collide"]}, "radius": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}, {"$ref": "#/refs/expr"}, {"$ref": "#/refs/paramField"}]}, "strength": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}], "default": 0.7}, "iterations": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}], "default": 1}}, "required": ["force"], "additionalProperties": false}, {"type": "object", "properties": {"force": {"enum": ["nbody"]}, "strength": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}], "default": -30}, "theta": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}], "default": 0.9}, "distanceMin": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}], "default": 1}, "distanceMax": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}}, "required": ["force"], "additionalProperties": false}, {"type": "object", "properties": {"force": {"enum": ["link"]}, "links": {"type": "string"}, "id": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}, "distance": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}, {"$ref": "#/refs/expr"}, {"$ref": "#/refs/paramField"}], "default": 30}, "strength": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}, {"$ref": "#/refs/expr"}, {"$ref": "#/refs/paramField"}]}, "iterations": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}], "default": 1}}, "required": ["force"], "additionalProperties": false}, {"type": "object", "properties": {"force": {"enum": ["x"]}, "strength": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}], "default": 0.1}, "x": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}}, "required": ["force"], "additionalProperties": false}, {"type": "object", "properties": {"force": {"enum": ["y"]}, "strength": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}], "default": 0.1}, "y": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}}, "required": ["force"], "additionalProperties": false}]}}, "as": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}], "default": ["x", "y", "vx", "vy"]}}, "required": ["type"], "additionalProperties": false}, "contourTransform": {"type": "object", "properties": {"type": {"enum": ["contour"]}, "signal": {"type": "string"}, "size": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}], "maxItems": 2, "minItems": 2}, "values": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}]}, "x": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}, "y": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}, "weight": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}, "cellSize": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}, "bandwidth": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}, "count": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}, "nice": {"anyOf": [{"type": "boolean"}, {"$ref": "#/refs/signal"}]}, "thresholds": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}]}, "smooth": {"anyOf": [{"type": "boolean"}, {"$ref": "#/refs/signal"}], "default": true}}, "required": ["type", "size"], "additionalProperties": false}, "geojsonTransform": {"type": "object", "properties": {"type": {"enum": ["g<PERSON><PERSON><PERSON>"]}, "signal": {"type": "string"}, "fields": {"oneOf": [{"type": "array", "items": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}}, {"$ref": "#/refs/signal"}], "maxItems": 2, "minItems": 2}, "geojson": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}}, "required": ["type"], "additionalProperties": false}, "geopathTransform": {"type": "object", "properties": {"type": {"enum": ["geopath"]}, "signal": {"type": "string"}, "projection": {"type": "string"}, "field": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}, "pointRadius": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}, {"$ref": "#/refs/expr"}, {"$ref": "#/refs/paramField"}]}, "as": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}], "default": "path"}}, "required": ["type"], "additionalProperties": false}, "geopointTransform": {"type": "object", "properties": {"type": {"enum": ["geopoint"]}, "signal": {"type": "string"}, "projection": {"type": "string"}, "fields": {"oneOf": [{"type": "array", "items": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}}, {"$ref": "#/refs/signal"}], "maxItems": 2, "minItems": 2}, "as": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}], "maxItems": 2, "minItems": 2, "default": ["x", "y"]}}, "required": ["type", "projection", "fields"], "additionalProperties": false}, "geoshapeTransform": {"type": "object", "properties": {"type": {"enum": ["geoshape"]}, "signal": {"type": "string"}, "projection": {"type": "string"}, "field": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}], "default": "datum"}, "pointRadius": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}, {"$ref": "#/refs/expr"}, {"$ref": "#/refs/paramField"}]}, "as": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}], "default": "shape"}}, "required": ["type"], "additionalProperties": false}, "graticuleTransform": {"type": "object", "properties": {"type": {"enum": ["graticule"]}, "signal": {"type": "string"}, "extent": {"oneOf": [{"type": "array", "items": {}}, {"$ref": "#/refs/signal"}], "maxItems": 2, "minItems": 2}, "extentMajor": {"oneOf": [{"type": "array", "items": {}}, {"$ref": "#/refs/signal"}], "maxItems": 2, "minItems": 2}, "extentMinor": {"oneOf": [{"type": "array", "items": {}}, {"$ref": "#/refs/signal"}], "maxItems": 2, "minItems": 2}, "step": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}], "maxItems": 2, "minItems": 2}, "stepMajor": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}], "maxItems": 2, "minItems": 2, "default": [90, 360]}, "stepMinor": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}], "maxItems": 2, "minItems": 2, "default": [10, 10]}, "precision": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}], "default": 2.5}}, "required": ["type"], "additionalProperties": false}, "heatmapTransform": {"type": "object", "properties": {"type": {"enum": ["heatmap"]}, "signal": {"type": "string"}, "field": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}, "color": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}, {"$ref": "#/refs/expr"}, {"$ref": "#/refs/paramField"}]}, "opacity": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}, {"$ref": "#/refs/expr"}, {"$ref": "#/refs/paramField"}]}, "resolve": {"anyOf": [{"enum": ["shared", "independent"]}, {"$ref": "#/refs/signal"}], "default": "independent"}, "as": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}], "default": "image"}}, "required": ["type"], "additionalProperties": false}, "isocontourTransform": {"type": "object", "properties": {"type": {"enum": ["isocontour"]}, "signal": {"type": "string"}, "field": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}, "thresholds": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}]}, "levels": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}, "nice": {"anyOf": [{"type": "boolean"}, {"$ref": "#/refs/signal"}]}, "resolve": {"anyOf": [{"enum": ["shared", "independent"]}, {"$ref": "#/refs/signal"}], "default": "independent"}, "zero": {"anyOf": [{"type": "boolean"}, {"$ref": "#/refs/signal"}], "default": true}, "smooth": {"anyOf": [{"type": "boolean"}, {"$ref": "#/refs/signal"}], "default": true}, "scale": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}, {"$ref": "#/refs/expr"}, {"$ref": "#/refs/paramField"}]}, "translate": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}, {"$ref": "#/refs/expr"}, {"$ref": "#/refs/paramField"}]}}, {"$ref": "#/refs/signal"}]}, "as": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}, {"type": "null"}], "default": "contour"}}, "required": ["type"], "additionalProperties": false}, "kde2dTransform": {"type": "object", "properties": {"type": {"enum": ["kde2d"]}, "signal": {"type": "string"}, "size": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}], "maxItems": 2, "minItems": 2}, "x": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}, "y": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}, "weight": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}, "groupby": {"oneOf": [{"type": "array", "items": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}}, {"$ref": "#/refs/signal"}]}, "cellSize": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}, "bandwidth": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}], "maxItems": 2, "minItems": 2}, "counts": {"anyOf": [{"type": "boolean"}, {"$ref": "#/refs/signal"}]}, "as": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}], "default": "grid"}}, "required": ["type", "size", "x", "y"], "additionalProperties": false}, "nestTransform": {"type": "object", "properties": {"type": {"enum": ["nest"]}, "signal": {"type": "string"}, "keys": {"oneOf": [{"type": "array", "items": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}}, {"$ref": "#/refs/signal"}]}, "generate": {"anyOf": [{"type": "boolean"}, {"$ref": "#/refs/signal"}]}}, "required": ["type"], "additionalProperties": false}, "packTransform": {"type": "object", "properties": {"type": {"enum": ["pack"]}, "signal": {"type": "string"}, "field": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}, "sort": {"$ref": "#/refs/compare"}, "padding": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}, "radius": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}, "size": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}], "maxItems": 2, "minItems": 2}, "as": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}], "maxItems": 5, "minItems": 5, "default": ["x", "y", "r", "depth", "children"]}}, "required": ["type"], "additionalProperties": false}, "partitionTransform": {"type": "object", "properties": {"type": {"enum": ["partition"]}, "signal": {"type": "string"}, "field": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}, "sort": {"$ref": "#/refs/compare"}, "padding": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}, "round": {"anyOf": [{"type": "boolean"}, {"$ref": "#/refs/signal"}]}, "size": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}], "maxItems": 2, "minItems": 2}, "as": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}], "maxItems": 6, "minItems": 6, "default": ["x0", "y0", "x1", "y1", "depth", "children"]}}, "required": ["type"], "additionalProperties": false}, "stratifyTransform": {"type": "object", "properties": {"type": {"enum": ["stratify"]}, "signal": {"type": "string"}, "key": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}, "parentKey": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}}, "required": ["type", "key", "parent<PERSON><PERSON>"], "additionalProperties": false}, "treeTransform": {"type": "object", "properties": {"type": {"enum": ["tree"]}, "signal": {"type": "string"}, "field": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}, "sort": {"$ref": "#/refs/compare"}, "method": {"anyOf": [{"enum": ["tidy", "cluster"]}, {"$ref": "#/refs/signal"}], "default": "tidy"}, "size": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}], "maxItems": 2, "minItems": 2}, "nodeSize": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}], "maxItems": 2, "minItems": 2}, "separation": {"anyOf": [{"type": "boolean"}, {"$ref": "#/refs/signal"}], "default": true}, "as": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}], "maxItems": 4, "minItems": 4, "default": ["x", "y", "depth", "children"]}}, "required": ["type"], "additionalProperties": false}, "treelinksTransform": {"type": "object", "properties": {"type": {"enum": ["treelinks"]}, "signal": {"type": "string"}}, "required": ["type"], "additionalProperties": false}, "treemapTransform": {"type": "object", "properties": {"type": {"enum": ["treemap"]}, "signal": {"type": "string"}, "field": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}, "sort": {"$ref": "#/refs/compare"}, "method": {"anyOf": [{"enum": ["squarify", "resquarify", "binary", "dice", "slice", "slicedice"]}, {"$ref": "#/refs/signal"}], "default": "squarify"}, "padding": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}, "paddingInner": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}, "paddingOuter": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}, "paddingTop": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}, "paddingRight": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}, "paddingBottom": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}, "paddingLeft": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}, "ratio": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}], "default": 1.618033988749895}, "round": {"anyOf": [{"type": "boolean"}, {"$ref": "#/refs/signal"}]}, "size": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}], "maxItems": 2, "minItems": 2}, "as": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}], "maxItems": 6, "minItems": 6, "default": ["x0", "y0", "x1", "y1", "depth", "children"]}}, "required": ["type"], "additionalProperties": false}, "loessTransform": {"type": "object", "properties": {"type": {"enum": ["loess"]}, "signal": {"type": "string"}, "x": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}, "y": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}, "groupby": {"oneOf": [{"type": "array", "items": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}}, {"$ref": "#/refs/signal"}]}, "bandwidth": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}], "default": 0.3}, "as": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}]}}, "required": ["type", "x", "y"], "additionalProperties": false}, "regressionTransform": {"type": "object", "properties": {"type": {"enum": ["regression"]}, "signal": {"type": "string"}, "x": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}, "y": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}, "groupby": {"oneOf": [{"type": "array", "items": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}}, {"$ref": "#/refs/signal"}]}, "method": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}], "default": "linear"}, "order": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}], "default": 3}, "extent": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}], "maxItems": 2, "minItems": 2}, "params": {"anyOf": [{"type": "boolean"}, {"$ref": "#/refs/signal"}]}, "as": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}]}}, "required": ["type", "x", "y"], "additionalProperties": false}, "aggregateTransform": {"type": "object", "properties": {"type": {"enum": ["aggregate"]}, "signal": {"type": "string"}, "groupby": {"oneOf": [{"type": "array", "items": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}}, {"$ref": "#/refs/signal"}]}, "ops": {"oneOf": [{"type": "array", "items": {"anyOf": [{"enum": ["values", "count", "__count__", "missing", "valid", "sum", "product", "mean", "average", "variance", "variancep", "stdev", "stdevp", "stderr", "distinct", "ci0", "ci1", "median", "q1", "q3", "a<PERSON><PERSON>", "argmax", "min", "max"]}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}]}, "fields": {"oneOf": [{"type": "array", "items": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}, {"type": "null"}]}}, {"$ref": "#/refs/signal"}]}, "as": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}, {"type": "null"}]}}, {"$ref": "#/refs/signal"}]}, "drop": {"anyOf": [{"type": "boolean"}, {"$ref": "#/refs/signal"}], "default": true}, "cross": {"anyOf": [{"type": "boolean"}, {"$ref": "#/refs/signal"}]}, "key": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}}, "required": ["type"], "additionalProperties": false}, "binTransform": {"type": "object", "properties": {"type": {"enum": ["bin"]}, "signal": {"type": "string"}, "field": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}, "interval": {"anyOf": [{"type": "boolean"}, {"$ref": "#/refs/signal"}], "default": true}, "anchor": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}, "maxbins": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}], "default": 20}, "base": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}], "default": 10}, "divide": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}], "default": [5, 2]}, "extent": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}], "maxItems": 2, "minItems": 2}, "span": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}, "step": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}, "steps": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}]}, "minstep": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}, "nice": {"anyOf": [{"type": "boolean"}, {"$ref": "#/refs/signal"}], "default": true}, "name": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}]}, "as": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}], "maxItems": 2, "minItems": 2, "default": ["bin0", "bin1"]}}, "required": ["type", "field", "extent"], "additionalProperties": false}, "collectTransform": {"type": "object", "properties": {"type": {"enum": ["collect"]}, "signal": {"type": "string"}, "sort": {"$ref": "#/refs/compare"}}, "required": ["type"], "additionalProperties": false}, "countpatternTransform": {"type": "object", "properties": {"type": {"enum": ["countpattern"]}, "signal": {"type": "string"}, "field": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}, "case": {"anyOf": [{"enum": ["upper", "lower", "mixed"]}, {"$ref": "#/refs/signal"}], "default": "mixed"}, "pattern": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}], "default": "[\\w\"]+"}, "stopwords": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}]}, "as": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}], "maxItems": 2, "minItems": 2, "default": ["text", "count"]}}, "required": ["type", "field"], "additionalProperties": false}, "crossTransform": {"type": "object", "properties": {"type": {"enum": ["cross"]}, "signal": {"type": "string"}, "filter": {"$ref": "#/refs/exprString"}, "as": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}], "maxItems": 2, "minItems": 2, "default": ["a", "b"]}}, "required": ["type"], "additionalProperties": false}, "densityTransform": {"type": "object", "properties": {"type": {"enum": ["density"]}, "signal": {"type": "string"}, "extent": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}], "maxItems": 2, "minItems": 2}, "steps": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}, "minsteps": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}], "default": 25}, "maxsteps": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}], "default": 200}, "method": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}], "default": "pdf"}, "distribution": {"oneOf": [{"type": "object", "properties": {"function": {"enum": ["normal"]}, "mean": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}, "stdev": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}], "default": 1}}, "required": ["function"], "additionalProperties": false}, {"type": "object", "properties": {"function": {"enum": ["lognormal"]}, "mean": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}, "stdev": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}], "default": 1}}, "required": ["function"], "additionalProperties": false}, {"type": "object", "properties": {"function": {"enum": ["uniform"]}, "min": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}, "max": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}], "default": 1}}, "required": ["function"], "additionalProperties": false}, {"type": "object", "properties": {"function": {"enum": ["kde"]}, "field": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}, "from": {"type": "string"}, "bandwidth": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}}, "required": ["function", "field"], "additionalProperties": false}, {"type": "object", "properties": {"function": {"enum": ["mixture"]}, "distributions": {"oneOf": [{"type": "array", "items": {}}, {"$ref": "#/refs/signal"}]}, "weights": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}]}}, "required": ["function"], "additionalProperties": false}]}, "as": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}], "default": ["value", "density"]}}, "required": ["type"], "additionalProperties": false}, "dotbinTransform": {"type": "object", "properties": {"type": {"enum": ["dotbin"]}, "signal": {"type": "string"}, "field": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}, "groupby": {"oneOf": [{"type": "array", "items": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}}, {"$ref": "#/refs/signal"}]}, "step": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}, "smooth": {"anyOf": [{"type": "boolean"}, {"$ref": "#/refs/signal"}]}, "as": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}], "default": "bin"}}, "required": ["type", "field"], "additionalProperties": false}, "extentTransform": {"type": "object", "properties": {"type": {"enum": ["extent"]}, "signal": {"type": "string"}, "field": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}}, "required": ["type", "field"], "additionalProperties": false}, "filterTransform": {"type": "object", "properties": {"type": {"enum": ["filter"]}, "signal": {"type": "string"}, "expr": {"$ref": "#/refs/exprString"}}, "required": ["type", "expr"], "additionalProperties": false}, "flattenTransform": {"type": "object", "properties": {"type": {"enum": ["flatten"]}, "signal": {"type": "string"}, "fields": {"oneOf": [{"type": "array", "items": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}}, {"$ref": "#/refs/signal"}]}, "index": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}]}, "as": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}]}}, "required": ["type", "fields"], "additionalProperties": false}, "foldTransform": {"type": "object", "properties": {"type": {"enum": ["fold"]}, "signal": {"type": "string"}, "fields": {"oneOf": [{"type": "array", "items": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}}, {"$ref": "#/refs/signal"}]}, "as": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}], "maxItems": 2, "minItems": 2, "default": ["key", "value"]}}, "required": ["type", "fields"], "additionalProperties": false}, "formulaTransform": {"type": "object", "properties": {"type": {"enum": ["formula"]}, "signal": {"type": "string"}, "expr": {"$ref": "#/refs/exprString"}, "as": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}]}, "initonly": {"anyOf": [{"type": "boolean"}, {"$ref": "#/refs/signal"}]}}, "required": ["type", "expr", "as"], "additionalProperties": false}, "imputeTransform": {"type": "object", "properties": {"type": {"enum": ["impute"]}, "signal": {"type": "string"}, "field": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}, "key": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}, "keyvals": {"oneOf": [{"type": "array", "items": {}}, {"$ref": "#/refs/signal"}]}, "groupby": {"oneOf": [{"type": "array", "items": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}}, {"$ref": "#/refs/signal"}]}, "method": {"anyOf": [{"enum": ["value", "mean", "median", "max", "min"]}, {"$ref": "#/refs/signal"}], "default": "value"}, "value": {}}, "required": ["type", "field", "key"], "additionalProperties": false}, "joinaggregateTransform": {"type": "object", "properties": {"type": {"enum": ["joinaggregate"]}, "signal": {"type": "string"}, "groupby": {"oneOf": [{"type": "array", "items": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}}, {"$ref": "#/refs/signal"}]}, "fields": {"oneOf": [{"type": "array", "items": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}, {"type": "null"}]}}, {"$ref": "#/refs/signal"}]}, "ops": {"oneOf": [{"type": "array", "items": {"anyOf": [{"enum": ["values", "count", "__count__", "missing", "valid", "sum", "product", "mean", "average", "variance", "variancep", "stdev", "stdevp", "stderr", "distinct", "ci0", "ci1", "median", "q1", "q3", "a<PERSON><PERSON>", "argmax", "min", "max"]}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}]}, "as": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}, {"type": "null"}]}}, {"$ref": "#/refs/signal"}]}, "key": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}}, "required": ["type"], "additionalProperties": false}, "kdeTransform": {"type": "object", "properties": {"type": {"enum": ["kde"]}, "signal": {"type": "string"}, "groupby": {"oneOf": [{"type": "array", "items": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}}, {"$ref": "#/refs/signal"}]}, "field": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}, "cumulative": {"anyOf": [{"type": "boolean"}, {"$ref": "#/refs/signal"}]}, "counts": {"anyOf": [{"type": "boolean"}, {"$ref": "#/refs/signal"}]}, "bandwidth": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}, "extent": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}], "maxItems": 2, "minItems": 2}, "resolve": {"anyOf": [{"enum": ["shared", "independent"]}, {"$ref": "#/refs/signal"}], "default": "independent"}, "steps": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}, "minsteps": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}], "default": 25}, "maxsteps": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}], "default": 200}, "as": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}], "default": ["value", "density"]}}, "required": ["type", "field"], "additionalProperties": false}, "lookupTransform": {"type": "object", "properties": {"type": {"enum": ["lookup"]}, "signal": {"type": "string"}, "from": {"type": "string"}, "key": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}, "values": {"oneOf": [{"type": "array", "items": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}}, {"$ref": "#/refs/signal"}]}, "fields": {"oneOf": [{"type": "array", "items": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}}, {"$ref": "#/refs/signal"}]}, "as": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}]}, "default": {}}, "required": ["type", "from", "key", "fields"], "additionalProperties": false}, "pivotTransform": {"type": "object", "properties": {"type": {"enum": ["pivot"]}, "signal": {"type": "string"}, "groupby": {"oneOf": [{"type": "array", "items": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}}, {"$ref": "#/refs/signal"}]}, "field": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}, "value": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}, "op": {"anyOf": [{"enum": ["values", "count", "__count__", "missing", "valid", "sum", "product", "mean", "average", "variance", "variancep", "stdev", "stdevp", "stderr", "distinct", "ci0", "ci1", "median", "q1", "q3", "a<PERSON><PERSON>", "argmax", "min", "max"]}, {"$ref": "#/refs/signal"}], "default": "sum"}, "limit": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}, "key": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}}, "required": ["type", "field", "value"], "additionalProperties": false}, "projectTransform": {"type": "object", "properties": {"type": {"enum": ["project"]}, "signal": {"type": "string"}, "fields": {"oneOf": [{"type": "array", "items": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}}, {"$ref": "#/refs/signal"}]}, "as": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}, {"type": "null"}]}}, {"$ref": "#/refs/signal"}]}}, "required": ["type"], "additionalProperties": false}, "quantileTransform": {"type": "object", "properties": {"type": {"enum": ["quantile"]}, "signal": {"type": "string"}, "groupby": {"oneOf": [{"type": "array", "items": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}}, {"$ref": "#/refs/signal"}]}, "field": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}, "probs": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}]}, "step": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}], "default": 0.01}, "as": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}], "default": ["prob", "value"]}}, "required": ["type", "field"], "additionalProperties": false}, "sampleTransform": {"type": "object", "properties": {"type": {"enum": ["sample"]}, "signal": {"type": "string"}, "size": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}], "default": 1000}}, "required": ["type"], "additionalProperties": false}, "sequenceTransform": {"type": "object", "properties": {"type": {"enum": ["sequence"]}, "signal": {"type": "string"}, "start": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}, "stop": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}, "step": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}], "default": 1}, "as": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}], "default": "data"}}, "required": ["type", "start", "stop"], "additionalProperties": false}, "timeunitTransform": {"type": "object", "properties": {"type": {"enum": ["timeunit"]}, "signal": {"type": "string"}, "field": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}, "interval": {"anyOf": [{"type": "boolean"}, {"$ref": "#/refs/signal"}], "default": true}, "units": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}]}, "step": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}], "default": 1}, "maxbins": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}], "default": 40}, "extent": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}]}, "timezone": {"anyOf": [{"enum": ["local", "utc"]}, {"$ref": "#/refs/signal"}], "default": "local"}, "as": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}], "maxItems": 2, "minItems": 2, "default": ["unit0", "unit1"]}}, "required": ["type", "field"], "additionalProperties": false}, "windowTransform": {"type": "object", "properties": {"type": {"enum": ["window"]}, "signal": {"type": "string"}, "sort": {"$ref": "#/refs/compare"}, "groupby": {"oneOf": [{"type": "array", "items": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}}, {"$ref": "#/refs/signal"}]}, "ops": {"oneOf": [{"type": "array", "items": {"anyOf": [{"enum": ["row_number", "rank", "dense_rank", "percent_rank", "cume_dist", "ntile", "lag", "lead", "first_value", "last_value", "nth_value", "prev_value", "next_value", "values", "count", "__count__", "missing", "valid", "sum", "product", "mean", "average", "variance", "variancep", "stdev", "stdevp", "stderr", "distinct", "ci0", "ci1", "median", "q1", "q3", "a<PERSON><PERSON>", "argmax", "min", "max"]}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}]}, "params": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}, {"type": "null"}]}}, {"$ref": "#/refs/signal"}]}, "fields": {"oneOf": [{"type": "array", "items": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}, {"type": "null"}]}}, {"$ref": "#/refs/signal"}]}, "as": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}, {"type": "null"}]}}, {"$ref": "#/refs/signal"}]}, "frame": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}, {"type": "null"}]}}, {"$ref": "#/refs/signal"}], "maxItems": 2, "minItems": 2, "default": [null, 0]}, "ignorePeers": {"anyOf": [{"type": "boolean"}, {"$ref": "#/refs/signal"}]}}, "required": ["type"], "additionalProperties": false}, "identifierTransform": {"type": "object", "properties": {"type": {"enum": ["identifier"]}, "signal": {"type": "string"}, "as": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}]}}, "required": ["type", "as"], "additionalProperties": false}, "voronoiTransform": {"type": "object", "properties": {"type": {"enum": ["voronoi"]}, "signal": {"type": "string"}, "x": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}, "y": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}, "size": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}], "maxItems": 2, "minItems": 2}, "extent": {"oneOf": [{"type": "array", "items": {}}, {"$ref": "#/refs/signal"}], "maxItems": 2, "minItems": 2, "default": [[-100000, -100000], [100000, 100000]]}, "as": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}], "default": "path"}}, "required": ["type", "x", "y"], "additionalProperties": false}, "wordcloudTransform": {"type": "object", "properties": {"type": {"enum": ["wordcloud"]}, "signal": {"type": "string"}, "size": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}], "maxItems": 2, "minItems": 2}, "font": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}, {"$ref": "#/refs/expr"}, {"$ref": "#/refs/paramField"}], "default": "sans-serif"}, "fontStyle": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}, {"$ref": "#/refs/expr"}, {"$ref": "#/refs/paramField"}], "default": "normal"}, "fontWeight": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}, {"$ref": "#/refs/expr"}, {"$ref": "#/refs/paramField"}], "default": "normal"}, "fontSize": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}, {"$ref": "#/refs/expr"}, {"$ref": "#/refs/paramField"}], "default": 14}, "fontSizeRange": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}, {"type": "null"}], "default": [10, 50]}, "rotate": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}, {"$ref": "#/refs/expr"}, {"$ref": "#/refs/paramField"}]}, "text": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/paramField"}, {"$ref": "#/refs/expr"}]}, "spiral": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}]}, "padding": {"anyOf": [{"type": "number"}, {"$ref": "#/refs/signal"}, {"$ref": "#/refs/expr"}, {"$ref": "#/refs/paramField"}]}, "as": {"oneOf": [{"type": "array", "items": {"anyOf": [{"type": "string"}, {"$ref": "#/refs/signal"}]}}, {"$ref": "#/refs/signal"}], "maxItems": 7, "minItems": 7, "default": ["x", "y", "font", "fontSize", "fontStyle", "fontWeight", "angle"]}}, "required": ["type"], "additionalProperties": false}}, "refs": {"labelOverlap": {"oneOf": [{"type": "boolean"}, {"enum": ["parity", "greedy"]}, {"$ref": "#/refs/signal"}]}, "tickBand": {"oneOf": [{"enum": ["center", "extent"]}, {"$ref": "#/refs/signal"}]}, "tickCount": {"oneOf": [{"type": "number"}, {"enum": ["millisecond", "second", "minute", "hour", "day", "week", "month", "year"]}, {"type": "object", "properties": {"interval": {"oneOf": [{"enum": ["millisecond", "second", "minute", "hour", "day", "week", "month", "year"]}, {"$ref": "#/refs/signal"}]}, "step": {"$ref": "#/refs/numberOrSignal"}}, "required": ["interval"], "additionalProperties": false}, {"$ref": "#/refs/signal"}]}, "element": {"type": "string"}, "paramField": {"type": "object", "properties": {"field": {"type": "string"}, "as": {"type": "string"}}, "required": ["field"], "additionalProperties": false}, "field": {"oneOf": [{"type": "string"}, {"$ref": "#/refs/signal"}, {"type": "object", "properties": {"datum": {"$ref": "#/refs/field"}}, "required": ["datum"], "additionalProperties": false}, {"type": "object", "properties": {"group": {"$ref": "#/refs/field"}, "level": {"type": "number"}}, "required": ["group"], "additionalProperties": false}, {"type": "object", "properties": {"parent": {"$ref": "#/refs/field"}, "level": {"type": "number"}}, "required": ["parent"], "additionalProperties": false}]}, "scale": {"$ref": "#/refs/field"}, "stringModifiers": {"type": "object", "properties": {"scale": {"$ref": "#/refs/scale"}}}, "numberModifiers": {"type": "object", "properties": {"exponent": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "mult": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "offset": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/numberValue"}]}, "round": {"type": "boolean", "default": false}, "scale": {"$ref": "#/refs/scale"}, "band": {"oneOf": [{"type": "number"}, {"type": "boolean"}]}, "extra": {"type": "boolean"}}}, "anyValue": {"oneOf": [{"type": "array", "items": {"allOf": [{"$ref": "#/defs/rule"}, {"allOf": [{"$ref": "#/refs/stringModifiers"}, {"anyOf": [{"oneOf": [{"$ref": "#/refs/signal"}, {"type": "object", "properties": {"value": {}}, "required": ["value"]}, {"type": "object", "properties": {"field": {"$ref": "#/refs/field"}}, "required": ["field"]}, {"type": "object", "properties": {"range": {"oneOf": [{"type": "number"}, {"type": "boolean"}]}}, "required": ["range"]}]}, {"type": "object", "required": ["scale", "value"]}, {"type": "object", "required": ["scale", "band"]}, {"type": "object", "required": ["offset"]}]}]}]}}, {"allOf": [{"$ref": "#/refs/stringModifiers"}, {"anyOf": [{"oneOf": [{"$ref": "#/refs/signal"}, {"type": "object", "properties": {"value": {}}, "required": ["value"]}, {"type": "object", "properties": {"field": {"$ref": "#/refs/field"}}, "required": ["field"]}, {"type": "object", "properties": {"range": {"oneOf": [{"type": "number"}, {"type": "boolean"}]}}, "required": ["range"]}]}, {"type": "object", "required": ["scale", "value"]}, {"type": "object", "required": ["scale", "band"]}, {"type": "object", "required": ["offset"]}]}]}]}, "blendValue": {"oneOf": [{"type": "array", "items": {"allOf": [{"$ref": "#/defs/rule"}, {"allOf": [{"$ref": "#/refs/stringModifiers"}, {"anyOf": [{"oneOf": [{"$ref": "#/refs/signal"}, {"type": "object", "properties": {"value": {"enum": [null, "multiply", "screen", "overlay", "darken", "lighten", "color-dodge", "color-burn", "hard-light", "soft-light", "difference", "exclusion", "hue", "saturation", "color", "luminosity"]}}, "required": ["value"]}, {"type": "object", "properties": {"field": {"$ref": "#/refs/field"}}, "required": ["field"]}, {"type": "object", "properties": {"range": {"oneOf": [{"type": "number"}, {"type": "boolean"}]}}, "required": ["range"]}]}, {"type": "object", "required": ["scale", "value"]}, {"type": "object", "required": ["scale", "band"]}, {"type": "object", "required": ["offset"]}]}]}]}}, {"allOf": [{"$ref": "#/refs/stringModifiers"}, {"anyOf": [{"oneOf": [{"$ref": "#/refs/signal"}, {"type": "object", "properties": {"value": {"enum": [null, "multiply", "screen", "overlay", "darken", "lighten", "color-dodge", "color-burn", "hard-light", "soft-light", "difference", "exclusion", "hue", "saturation", "color", "luminosity"]}}, "required": ["value"]}, {"type": "object", "properties": {"field": {"$ref": "#/refs/field"}}, "required": ["field"]}, {"type": "object", "properties": {"range": {"oneOf": [{"type": "number"}, {"type": "boolean"}]}}, "required": ["range"]}]}, {"type": "object", "required": ["scale", "value"]}, {"type": "object", "required": ["scale", "band"]}, {"type": "object", "required": ["offset"]}]}]}]}, "numberValue": {"oneOf": [{"type": "array", "items": {"allOf": [{"$ref": "#/defs/rule"}, {"allOf": [{"$ref": "#/refs/numberModifiers"}, {"anyOf": [{"oneOf": [{"$ref": "#/refs/signal"}, {"type": "object", "properties": {"value": {"type": "number"}}, "required": ["value"]}, {"type": "object", "properties": {"field": {"$ref": "#/refs/field"}}, "required": ["field"]}, {"type": "object", "properties": {"range": {"oneOf": [{"type": "number"}, {"type": "boolean"}]}}, "required": ["range"]}]}, {"type": "object", "required": ["scale", "value"]}, {"type": "object", "required": ["scale", "band"]}, {"type": "object", "required": ["offset"]}]}]}]}}, {"allOf": [{"$ref": "#/refs/numberModifiers"}, {"anyOf": [{"oneOf": [{"$ref": "#/refs/signal"}, {"type": "object", "properties": {"value": {"type": "number"}}, "required": ["value"]}, {"type": "object", "properties": {"field": {"$ref": "#/refs/field"}}, "required": ["field"]}, {"type": "object", "properties": {"range": {"oneOf": [{"type": "number"}, {"type": "boolean"}]}}, "required": ["range"]}]}, {"type": "object", "required": ["scale", "value"]}, {"type": "object", "required": ["scale", "band"]}, {"type": "object", "required": ["offset"]}]}]}]}, "stringValue": {"oneOf": [{"type": "array", "items": {"allOf": [{"$ref": "#/defs/rule"}, {"allOf": [{"$ref": "#/refs/stringModifiers"}, {"anyOf": [{"oneOf": [{"$ref": "#/refs/signal"}, {"type": "object", "properties": {"value": {"type": "string"}}, "required": ["value"]}, {"type": "object", "properties": {"field": {"$ref": "#/refs/field"}}, "required": ["field"]}, {"type": "object", "properties": {"range": {"oneOf": [{"type": "number"}, {"type": "boolean"}]}}, "required": ["range"]}]}, {"type": "object", "required": ["scale", "value"]}, {"type": "object", "required": ["scale", "band"]}, {"type": "object", "required": ["offset"]}]}]}]}}, {"allOf": [{"$ref": "#/refs/stringModifiers"}, {"anyOf": [{"oneOf": [{"$ref": "#/refs/signal"}, {"type": "object", "properties": {"value": {"type": "string"}}, "required": ["value"]}, {"type": "object", "properties": {"field": {"$ref": "#/refs/field"}}, "required": ["field"]}, {"type": "object", "properties": {"range": {"oneOf": [{"type": "number"}, {"type": "boolean"}]}}, "required": ["range"]}]}, {"type": "object", "required": ["scale", "value"]}, {"type": "object", "required": ["scale", "band"]}, {"type": "object", "required": ["offset"]}]}]}]}, "textValue": {"oneOf": [{"type": "array", "items": {"allOf": [{"$ref": "#/defs/rule"}, {"allOf": [{"$ref": "#/refs/stringModifiers"}, {"anyOf": [{"oneOf": [{"$ref": "#/refs/signal"}, {"type": "object", "properties": {"value": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}}, "required": ["value"]}, {"type": "object", "properties": {"field": {"$ref": "#/refs/field"}}, "required": ["field"]}, {"type": "object", "properties": {"range": {"oneOf": [{"type": "number"}, {"type": "boolean"}]}}, "required": ["range"]}]}, {"type": "object", "required": ["scale", "value"]}, {"type": "object", "required": ["scale", "band"]}, {"type": "object", "required": ["offset"]}]}]}]}}, {"allOf": [{"$ref": "#/refs/stringModifiers"}, {"anyOf": [{"oneOf": [{"$ref": "#/refs/signal"}, {"type": "object", "properties": {"value": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}}, "required": ["value"]}, {"type": "object", "properties": {"field": {"$ref": "#/refs/field"}}, "required": ["field"]}, {"type": "object", "properties": {"range": {"oneOf": [{"type": "number"}, {"type": "boolean"}]}}, "required": ["range"]}]}, {"type": "object", "required": ["scale", "value"]}, {"type": "object", "required": ["scale", "band"]}, {"type": "object", "required": ["offset"]}]}]}]}, "booleanValue": {"oneOf": [{"type": "array", "items": {"allOf": [{"$ref": "#/defs/rule"}, {"allOf": [{"$ref": "#/refs/stringModifiers"}, {"anyOf": [{"oneOf": [{"$ref": "#/refs/signal"}, {"type": "object", "properties": {"value": {"type": "boolean"}}, "required": ["value"]}, {"type": "object", "properties": {"field": {"$ref": "#/refs/field"}}, "required": ["field"]}, {"type": "object", "properties": {"range": {"oneOf": [{"type": "number"}, {"type": "boolean"}]}}, "required": ["range"]}]}, {"type": "object", "required": ["scale", "value"]}, {"type": "object", "required": ["scale", "band"]}, {"type": "object", "required": ["offset"]}]}]}]}}, {"allOf": [{"$ref": "#/refs/stringModifiers"}, {"anyOf": [{"oneOf": [{"$ref": "#/refs/signal"}, {"type": "object", "properties": {"value": {"type": "boolean"}}, "required": ["value"]}, {"type": "object", "properties": {"field": {"$ref": "#/refs/field"}}, "required": ["field"]}, {"type": "object", "properties": {"range": {"oneOf": [{"type": "number"}, {"type": "boolean"}]}}, "required": ["range"]}]}, {"type": "object", "required": ["scale", "value"]}, {"type": "object", "required": ["scale", "band"]}, {"type": "object", "required": ["offset"]}]}]}]}, "arrayValue": {"oneOf": [{"type": "array", "items": {"allOf": [{"$ref": "#/defs/rule"}, {"allOf": [{"$ref": "#/refs/stringModifiers"}, {"anyOf": [{"oneOf": [{"$ref": "#/refs/signal"}, {"type": "object", "properties": {"value": {"type": "array"}}, "required": ["value"]}, {"type": "object", "properties": {"field": {"$ref": "#/refs/field"}}, "required": ["field"]}, {"type": "object", "properties": {"range": {"oneOf": [{"type": "number"}, {"type": "boolean"}]}}, "required": ["range"]}]}, {"type": "object", "required": ["scale", "value"]}, {"type": "object", "required": ["scale", "band"]}, {"type": "object", "required": ["offset"]}]}]}]}}, {"allOf": [{"$ref": "#/refs/stringModifiers"}, {"anyOf": [{"oneOf": [{"$ref": "#/refs/signal"}, {"type": "object", "properties": {"value": {"type": "array"}}, "required": ["value"]}, {"type": "object", "properties": {"field": {"$ref": "#/refs/field"}}, "required": ["field"]}, {"type": "object", "properties": {"range": {"oneOf": [{"type": "number"}, {"type": "boolean"}]}}, "required": ["range"]}]}, {"type": "object", "required": ["scale", "value"]}, {"type": "object", "required": ["scale", "band"]}, {"type": "object", "required": ["offset"]}]}]}]}, "fontWeightValue": {"oneOf": [{"type": "array", "items": {"allOf": [{"$ref": "#/defs/rule"}, {"allOf": [{"$ref": "#/refs/stringModifiers"}, {"anyOf": [{"oneOf": [{"$ref": "#/refs/signal"}, {"type": "object", "properties": {"value": {"enum": [null, "normal", "bold", "lighter", "bolder", "100", "200", "300", "400", "500", "600", "700", "800", "900", 100, 200, 300, 400, 500, 600, 700, 800, 900]}}, "required": ["value"]}, {"type": "object", "properties": {"field": {"$ref": "#/refs/field"}}, "required": ["field"]}, {"type": "object", "properties": {"range": {"oneOf": [{"type": "number"}, {"type": "boolean"}]}}, "required": ["range"]}]}, {"type": "object", "required": ["scale", "value"]}, {"type": "object", "required": ["scale", "band"]}, {"type": "object", "required": ["offset"]}]}]}]}}, {"allOf": [{"$ref": "#/refs/stringModifiers"}, {"anyOf": [{"oneOf": [{"$ref": "#/refs/signal"}, {"type": "object", "properties": {"value": {"enum": [null, "normal", "bold", "lighter", "bolder", "100", "200", "300", "400", "500", "600", "700", "800", "900", 100, 200, 300, 400, 500, 600, 700, 800, 900]}}, "required": ["value"]}, {"type": "object", "properties": {"field": {"$ref": "#/refs/field"}}, "required": ["field"]}, {"type": "object", "properties": {"range": {"oneOf": [{"type": "number"}, {"type": "boolean"}]}}, "required": ["range"]}]}, {"type": "object", "required": ["scale", "value"]}, {"type": "object", "required": ["scale", "band"]}, {"type": "object", "required": ["offset"]}]}]}]}, "anchorValue": {"oneOf": [{"type": "array", "items": {"allOf": [{"$ref": "#/defs/rule"}, {"allOf": [{"$ref": "#/refs/stringModifiers"}, {"anyOf": [{"oneOf": [{"$ref": "#/refs/signal"}, {"type": "object", "properties": {"value": {"enum": ["start", "middle", "end"]}}, "required": ["value"]}, {"type": "object", "properties": {"field": {"$ref": "#/refs/field"}}, "required": ["field"]}, {"type": "object", "properties": {"range": {"oneOf": [{"type": "number"}, {"type": "boolean"}]}}, "required": ["range"]}]}, {"type": "object", "required": ["scale", "value"]}, {"type": "object", "required": ["scale", "band"]}, {"type": "object", "required": ["offset"]}]}]}]}}, {"allOf": [{"$ref": "#/refs/stringModifiers"}, {"anyOf": [{"oneOf": [{"$ref": "#/refs/signal"}, {"type": "object", "properties": {"value": {"enum": ["start", "middle", "end"]}}, "required": ["value"]}, {"type": "object", "properties": {"field": {"$ref": "#/refs/field"}}, "required": ["field"]}, {"type": "object", "properties": {"range": {"oneOf": [{"type": "number"}, {"type": "boolean"}]}}, "required": ["range"]}]}, {"type": "object", "required": ["scale", "value"]}, {"type": "object", "required": ["scale", "band"]}, {"type": "object", "required": ["offset"]}]}]}]}, "alignValue": {"oneOf": [{"type": "array", "items": {"allOf": [{"$ref": "#/defs/rule"}, {"allOf": [{"$ref": "#/refs/stringModifiers"}, {"anyOf": [{"oneOf": [{"$ref": "#/refs/signal"}, {"type": "object", "properties": {"value": {"enum": ["left", "right", "center"]}}, "required": ["value"]}, {"type": "object", "properties": {"field": {"$ref": "#/refs/field"}}, "required": ["field"]}, {"type": "object", "properties": {"range": {"oneOf": [{"type": "number"}, {"type": "boolean"}]}}, "required": ["range"]}]}, {"type": "object", "required": ["scale", "value"]}, {"type": "object", "required": ["scale", "band"]}, {"type": "object", "required": ["offset"]}]}]}]}}, {"allOf": [{"$ref": "#/refs/stringModifiers"}, {"anyOf": [{"oneOf": [{"$ref": "#/refs/signal"}, {"type": "object", "properties": {"value": {"enum": ["left", "right", "center"]}}, "required": ["value"]}, {"type": "object", "properties": {"field": {"$ref": "#/refs/field"}}, "required": ["field"]}, {"type": "object", "properties": {"range": {"oneOf": [{"type": "number"}, {"type": "boolean"}]}}, "required": ["range"]}]}, {"type": "object", "required": ["scale", "value"]}, {"type": "object", "required": ["scale", "band"]}, {"type": "object", "required": ["offset"]}]}]}]}, "baselineValue": {"oneOf": [{"type": "array", "items": {"allOf": [{"$ref": "#/defs/rule"}, {"allOf": [{"$ref": "#/refs/stringModifiers"}, {"anyOf": [{"oneOf": [{"$ref": "#/refs/signal"}, {"type": "object", "properties": {"value": {"enum": ["top", "middle", "bottom", "alphabetic"]}}, "required": ["value"]}, {"type": "object", "properties": {"field": {"$ref": "#/refs/field"}}, "required": ["field"]}, {"type": "object", "properties": {"range": {"oneOf": [{"type": "number"}, {"type": "boolean"}]}}, "required": ["range"]}]}, {"type": "object", "required": ["scale", "value"]}, {"type": "object", "required": ["scale", "band"]}, {"type": "object", "required": ["offset"]}]}]}]}}, {"allOf": [{"$ref": "#/refs/stringModifiers"}, {"anyOf": [{"oneOf": [{"$ref": "#/refs/signal"}, {"type": "object", "properties": {"value": {"enum": ["top", "middle", "bottom", "alphabetic"]}}, "required": ["value"]}, {"type": "object", "properties": {"field": {"$ref": "#/refs/field"}}, "required": ["field"]}, {"type": "object", "properties": {"range": {"oneOf": [{"type": "number"}, {"type": "boolean"}]}}, "required": ["range"]}]}, {"type": "object", "required": ["scale", "value"]}, {"type": "object", "required": ["scale", "band"]}, {"type": "object", "required": ["offset"]}]}]}]}, "directionValue": {"oneOf": [{"type": "array", "items": {"allOf": [{"$ref": "#/defs/rule"}, {"allOf": [{"$ref": "#/refs/stringModifiers"}, {"anyOf": [{"oneOf": [{"$ref": "#/refs/signal"}, {"type": "object", "properties": {"value": {"enum": ["horizontal", "vertical"]}}, "required": ["value"]}, {"type": "object", "properties": {"field": {"$ref": "#/refs/field"}}, "required": ["field"]}, {"type": "object", "properties": {"range": {"oneOf": [{"type": "number"}, {"type": "boolean"}]}}, "required": ["range"]}]}, {"type": "object", "required": ["scale", "value"]}, {"type": "object", "required": ["scale", "band"]}, {"type": "object", "required": ["offset"]}]}]}]}}, {"allOf": [{"$ref": "#/refs/stringModifiers"}, {"anyOf": [{"oneOf": [{"$ref": "#/refs/signal"}, {"type": "object", "properties": {"value": {"enum": ["horizontal", "vertical"]}}, "required": ["value"]}, {"type": "object", "properties": {"field": {"$ref": "#/refs/field"}}, "required": ["field"]}, {"type": "object", "properties": {"range": {"oneOf": [{"type": "number"}, {"type": "boolean"}]}}, "required": ["range"]}]}, {"type": "object", "required": ["scale", "value"]}, {"type": "object", "required": ["scale", "band"]}, {"type": "object", "required": ["offset"]}]}]}]}, "orientValue": {"oneOf": [{"type": "array", "items": {"allOf": [{"$ref": "#/defs/rule"}, {"allOf": [{"$ref": "#/refs/stringModifiers"}, {"anyOf": [{"oneOf": [{"$ref": "#/refs/signal"}, {"type": "object", "properties": {"value": {"enum": ["left", "right", "top", "bottom"]}}, "required": ["value"]}, {"type": "object", "properties": {"field": {"$ref": "#/refs/field"}}, "required": ["field"]}, {"type": "object", "properties": {"range": {"oneOf": [{"type": "number"}, {"type": "boolean"}]}}, "required": ["range"]}]}, {"type": "object", "required": ["scale", "value"]}, {"type": "object", "required": ["scale", "band"]}, {"type": "object", "required": ["offset"]}]}]}]}}, {"allOf": [{"$ref": "#/refs/stringModifiers"}, {"anyOf": [{"oneOf": [{"$ref": "#/refs/signal"}, {"type": "object", "properties": {"value": {"enum": ["left", "right", "top", "bottom"]}}, "required": ["value"]}, {"type": "object", "properties": {"field": {"$ref": "#/refs/field"}}, "required": ["field"]}, {"type": "object", "properties": {"range": {"oneOf": [{"type": "number"}, {"type": "boolean"}]}}, "required": ["range"]}]}, {"type": "object", "required": ["scale", "value"]}, {"type": "object", "required": ["scale", "band"]}, {"type": "object", "required": ["offset"]}]}]}]}, "strokeCapValue": {"oneOf": [{"type": "array", "items": {"allOf": [{"$ref": "#/defs/rule"}, {"allOf": [{"$ref": "#/refs/stringModifiers"}, {"anyOf": [{"oneOf": [{"$ref": "#/refs/signal"}, {"type": "object", "properties": {"value": {"enum": ["butt", "round", "square"]}}, "required": ["value"]}, {"type": "object", "properties": {"field": {"$ref": "#/refs/field"}}, "required": ["field"]}, {"type": "object", "properties": {"range": {"oneOf": [{"type": "number"}, {"type": "boolean"}]}}, "required": ["range"]}]}, {"type": "object", "required": ["scale", "value"]}, {"type": "object", "required": ["scale", "band"]}, {"type": "object", "required": ["offset"]}]}]}]}}, {"allOf": [{"$ref": "#/refs/stringModifiers"}, {"anyOf": [{"oneOf": [{"$ref": "#/refs/signal"}, {"type": "object", "properties": {"value": {"enum": ["butt", "round", "square"]}}, "required": ["value"]}, {"type": "object", "properties": {"field": {"$ref": "#/refs/field"}}, "required": ["field"]}, {"type": "object", "properties": {"range": {"oneOf": [{"type": "number"}, {"type": "boolean"}]}}, "required": ["range"]}]}, {"type": "object", "required": ["scale", "value"]}, {"type": "object", "required": ["scale", "band"]}, {"type": "object", "required": ["offset"]}]}]}]}, "strokeJoinValue": {"oneOf": [{"type": "array", "items": {"allOf": [{"$ref": "#/defs/rule"}, {"allOf": [{"$ref": "#/refs/stringModifiers"}, {"anyOf": [{"oneOf": [{"$ref": "#/refs/signal"}, {"type": "object", "properties": {"value": {"enum": ["miter", "round", "bevel"]}}, "required": ["value"]}, {"type": "object", "properties": {"field": {"$ref": "#/refs/field"}}, "required": ["field"]}, {"type": "object", "properties": {"range": {"oneOf": [{"type": "number"}, {"type": "boolean"}]}}, "required": ["range"]}]}, {"type": "object", "required": ["scale", "value"]}, {"type": "object", "required": ["scale", "band"]}, {"type": "object", "required": ["offset"]}]}]}]}}, {"allOf": [{"$ref": "#/refs/stringModifiers"}, {"anyOf": [{"oneOf": [{"$ref": "#/refs/signal"}, {"type": "object", "properties": {"value": {"enum": ["miter", "round", "bevel"]}}, "required": ["value"]}, {"type": "object", "properties": {"field": {"$ref": "#/refs/field"}}, "required": ["field"]}, {"type": "object", "properties": {"range": {"oneOf": [{"type": "number"}, {"type": "boolean"}]}}, "required": ["range"]}]}, {"type": "object", "required": ["scale", "value"]}, {"type": "object", "required": ["scale", "band"]}, {"type": "object", "required": ["offset"]}]}]}]}, "baseColorValue": {"oneOf": [{"allOf": [{"$ref": "#/refs/stringModifiers"}, {"anyOf": [{"oneOf": [{"$ref": "#/refs/signal"}, {"type": "object", "properties": {"value": {"oneOf": [{"type": "string"}, {"type": "null"}]}}, "required": ["value"]}, {"type": "object", "properties": {"field": {"$ref": "#/refs/field"}}, "required": ["field"]}, {"type": "object", "properties": {"range": {"oneOf": [{"type": "number"}, {"type": "boolean"}]}}, "required": ["range"]}]}, {"type": "object", "required": ["scale", "value"]}, {"type": "object", "required": ["scale", "band"]}, {"type": "object", "required": ["offset"]}]}]}, {"type": "object", "properties": {"value": {"$ref": "#/refs/linearGradient"}}, "required": ["value"], "additionalProperties": false}, {"type": "object", "properties": {"value": {"$ref": "#/refs/radialGradient"}}, "required": ["value"], "additionalProperties": false}, {"type": "object", "properties": {"gradient": {"$ref": "#/refs/scale"}, "start": {"type": "array", "items": {"type": "number"}, "minItems": 2, "maxItems": 2}, "stop": {"type": "array", "items": {"type": "number"}, "minItems": 2, "maxItems": 2}, "count": {"type": "number"}}, "required": ["gradient"], "additionalProperties": false}, {"type": "object", "properties": {"color": {"oneOf": [{"$ref": "#/refs/colorRGB"}, {"$ref": "#/refs/colorHSL"}, {"$ref": "#/refs/colorLAB"}, {"$ref": "#/refs/colorHCL"}]}}, "required": ["color"], "additionalProperties": false}]}, "colorRGB": {"type": "object", "properties": {"r": {"$ref": "#/refs/numberValue"}, "g": {"$ref": "#/refs/numberValue"}, "b": {"$ref": "#/refs/numberValue"}}, "required": ["r", "g", "b"]}, "colorHSL": {"type": "object", "properties": {"h": {"$ref": "#/refs/numberValue"}, "s": {"$ref": "#/refs/numberValue"}, "l": {"$ref": "#/refs/numberValue"}}, "required": ["h", "s", "l"]}, "colorLAB": {"type": "object", "properties": {"l": {"$ref": "#/refs/numberValue"}, "a": {"$ref": "#/refs/numberValue"}, "b": {"$ref": "#/refs/numberValue"}}, "required": ["l", "a", "b"]}, "colorHCL": {"type": "object", "properties": {"h": {"$ref": "#/refs/numberValue"}, "c": {"$ref": "#/refs/numberValue"}, "l": {"$ref": "#/refs/numberValue"}}, "required": ["h", "c", "l"]}, "colorValue": {"oneOf": [{"type": "array", "items": {"allOf": [{"$ref": "#/defs/rule"}, {"$ref": "#/refs/baseColorValue"}]}}, {"$ref": "#/refs/baseColorValue"}]}, "gradientStops": {"type": "array", "items": {"type": "object", "properties": {"offset": {"type": "number"}, "color": {"type": "string"}}, "required": ["offset", "color"], "additionalProperties": false}}, "linearGradient": {"type": "object", "properties": {"gradient": {"enum": ["linear"]}, "id": {"type": "string"}, "x1": {"type": "number"}, "y1": {"type": "number"}, "x2": {"type": "number"}, "y2": {"type": "number"}, "stops": {"$ref": "#/refs/gradientStops"}}, "required": ["gradient", "stops"], "additionalProperties": false}, "radialGradient": {"type": "object", "properties": {"gradient": {"enum": ["radial"]}, "id": {"type": "string"}, "x1": {"type": "number"}, "y1": {"type": "number"}, "r1": {"type": "number"}, "x2": {"type": "number"}, "y2": {"type": "number"}, "r2": {"type": "number"}, "stops": {"$ref": "#/refs/gradientStops"}}, "required": ["gradient", "stops"], "additionalProperties": false}, "expr": {"type": "object", "properties": {"expr": {"type": "string"}, "as": {"type": "string"}}, "required": ["expr"]}, "exprString": {"type": "string"}, "compare": {"oneOf": [{"type": "object", "properties": {"field": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/expr"}]}, "order": {"$ref": "#/refs/sortOrder"}}, "additionalProperties": false}, {"type": "object", "properties": {"field": {"type": "array", "items": {"oneOf": [{"$ref": "#/refs/scaleField"}, {"$ref": "#/refs/expr"}]}}, "order": {"type": "array", "items": {"$ref": "#/refs/sortOrder"}}}, "additionalProperties": false}]}, "from": {"type": "object", "properties": {"data": {"type": "string"}}, "additionalProperties": false}, "facet": {"type": "object", "properties": {"data": {"type": "string"}, "facet": {"oneOf": [{"type": "object", "properties": {"name": {"type": "string"}, "data": {"type": "string"}, "field": {"type": "string"}}, "required": ["name", "data", "field"], "additionalProperties": false}, {"type": "object", "properties": {"name": {"type": "string"}, "data": {"type": "string"}, "groupby": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "aggregate": {"type": "object", "properties": {"cross": {"type": "boolean"}, "fields": {"type": "array", "items": {"type": "string"}}, "ops": {"type": "array", "items": {"type": "string"}}, "as": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false}}, "required": ["name", "data", "groupby"], "additionalProperties": false}]}}, "required": ["facet"], "additionalProperties": false}, "markclip": {"oneOf": [{"$ref": "#/refs/booleanOrSignal"}, {"type": "object", "properties": {"path": {"$ref": "#/refs/stringOrSignal"}}, "required": ["path"], "additionalProperties": false}, {"type": "object", "properties": {"sphere": {"$ref": "#/refs/stringOrSignal"}}, "required": ["sphere"], "additionalProperties": false}]}, "style": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "marktype": {"type": "string"}, "scaleField": {"$ref": "#/refs/stringOrSignal"}, "sortOrder": {"oneOf": [{"enum": ["ascending", "descending"]}, {"$ref": "#/refs/signal"}]}, "scaleBins": {"oneOf": [{"type": "array", "items": {"$ref": "#/refs/numberOrSignal"}}, {"type": "object", "properties": {"step": {"$ref": "#/refs/numberOrSignal"}, "start": {"$ref": "#/refs/numberOrSignal"}, "stop": {"$ref": "#/refs/numberOrSignal"}}, "required": ["step"], "additionalProperties": false}, {"$ref": "#/refs/signal"}]}, "scaleInterpolate": {"oneOf": [{"type": "string"}, {"$ref": "#/refs/signal"}, {"type": "object", "properties": {"type": {"$ref": "#/refs/stringOrSignal"}, "gamma": {"$ref": "#/refs/numberOrSignal"}}, "required": ["type"], "additionalProperties": false}]}, "scaleData": {"oneOf": [{"type": "object", "properties": {"data": {"type": "string"}, "field": {"$ref": "#/refs/stringOrSignal"}, "sort": {"oneOf": [{"type": "boolean"}, {"type": "object", "properties": {"field": {"$ref": "#/refs/stringOrSignal"}, "op": {"$ref": "#/refs/stringOrSignal"}, "order": {"$ref": "#/refs/sortOrder"}}, "additionalProperties": false}]}}, "required": ["data", "field"], "additionalProperties": false}, {"type": "object", "properties": {"data": {"type": "string"}, "fields": {"type": "array", "items": {"$ref": "#/refs/stringOrSignal"}, "minItems": 1}, "sort": {"oneOf": [{"type": "boolean"}, {"type": "object", "properties": {"op": {"enum": ["count"]}, "order": {"$ref": "#/refs/sortOrder"}}, "additionalProperties": false}, {"type": "object", "properties": {"field": {"$ref": "#/refs/stringOrSignal"}, "op": {"enum": ["count", "min", "max"]}, "order": {"$ref": "#/refs/sortOrder"}}, "required": ["field", "op"], "additionalProperties": false}]}}, "required": ["data", "fields"], "additionalProperties": false}, {"type": "object", "properties": {"fields": {"type": "array", "items": {"oneOf": [{"type": "object", "properties": {"data": {"type": "string"}, "field": {"$ref": "#/refs/stringOrSignal"}}, "required": ["data", "field"], "additionalProperties": false}, {"type": "array", "items": {"oneOf": [{"type": "string"}, {"type": "number"}, {"type": "boolean"}]}}, {"$ref": "#/refs/signal"}]}, "minItems": 1}, "sort": {"oneOf": [{"type": "boolean"}, {"type": "object", "properties": {"op": {"enum": ["count"]}, "order": {"$ref": "#/refs/sortOrder"}}, "additionalProperties": false}, {"type": "object", "properties": {"field": {"$ref": "#/refs/stringOrSignal"}, "op": {"enum": ["count", "min", "max"]}, "order": {"$ref": "#/refs/sortOrder"}}, "required": ["field", "op"], "additionalProperties": false}]}}, "required": ["fields"], "additionalProperties": false}]}, "selector": {"type": "string"}, "signal": {"type": "object", "properties": {"signal": {"type": "string"}}, "required": ["signal"]}, "arrayOrSignal": {"oneOf": [{"type": "array"}, {"$ref": "#/refs/signal"}]}, "booleanOrSignal": {"oneOf": [{"type": "boolean"}, {"$ref": "#/refs/signal"}]}, "numberOrSignal": {"oneOf": [{"type": "number"}, {"$ref": "#/refs/signal"}]}, "stringOrSignal": {"oneOf": [{"type": "string"}, {"$ref": "#/refs/signal"}]}, "textOrSignal": {"oneOf": [{"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, {"$ref": "#/refs/signal"}]}}, "type": "object", "allOf": [{"$ref": "#/defs/scope"}, {"properties": {"$schema": {"type": "string", "format": "uri"}, "config": {"type": "object"}, "description": {"type": "string"}, "width": {"$ref": "#/refs/numberOrSignal"}, "height": {"$ref": "#/refs/numberOrSignal"}, "padding": {"$ref": "#/defs/padding"}, "autosize": {"$ref": "#/defs/autosize"}, "background": {"$ref": "#/defs/background"}, "style": {"$ref": "#/refs/style"}}}]}