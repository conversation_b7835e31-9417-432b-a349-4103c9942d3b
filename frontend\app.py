# frontend/app.py
import streamlit as st
import requests
import time
from audio_recorder_streamlit import audio_recorder

st.set_page_config(page_title="🩺 SOAP Note Generator", layout="wide")

BACKEND_URL = "http://localhost:8000"

st.title("🩺 SOAP Note Generator")
st.markdown("**Real-time Doctor-Patient Conversation Recorder**")

# Initialize session state
if 'conversation' not in st.session_state:
    st.session_state.conversation = []
if 'current_speaker' not in st.session_state:
    st.session_state.current_speaker = "Doctor"

# Create two columns
col1, col2 = st.columns([1, 1])

with col1:
    st.subheader("🎤 Live Recording")

    # Speaker selection
    speaker = st.selectbox("Who is speaking?", ["Doctor", "Patient"],
                          index=0 if st.session_state.current_speaker == "Doctor" else 1)

    # Audio recorder
    audio_bytes = audio_recorder(
        text="Click to record",
        recording_color="#e74c3c",
        neutral_color="#34495e",
        icon_name="microphone",
        icon_size="2x",
    )

    # Manual text input
    st.markdown("**Or type manually:**")
    manual_text = st.text_area("Enter speech text:", height=100)

    col_add, col_clear = st.columns(2)
    with col_add:
        if st.button("➕ Add to Conversation"):
            if manual_text.strip():
                # Add to conversation
                data = {"speaker": speaker, "text": manual_text.strip()}
                response = requests.post(f"{BACKEND_URL}/add_speech/", data=data)
                if response.ok:
                    st.session_state.conversation = response.json()["conversation"]
                    st.session_state.current_speaker = "Patient" if speaker == "Doctor" else "Doctor"
                    st.rerun()

    with col_clear:
        if st.button("🗑️ Clear All"):
            response = requests.post(f"{BACKEND_URL}/clear_conversation/")
            if response.ok:
                st.session_state.conversation = []
                st.rerun()

with col2:
    st.subheader("💬 Live Conversation")

    # Display conversation
    if st.session_state.conversation:
        for i, item in enumerate(st.session_state.conversation):
            speaker_emoji = "👨‍⚕️" if item["speaker"] == "Doctor" else "🤒"
            with st.container():
                st.markdown(f"**{speaker_emoji} {item['speaker']}:**")
                st.markdown(f"*{item['text']}*")
                st.markdown("---")
    else:
        st.info("No conversation yet. Start recording!")

# Generate SOAP Note
st.markdown("---")
col_soap1, col_soap2 = st.columns([1, 2])

with col_soap1:
    if st.button("📋 Generate SOAP Note", type="primary"):
        if st.session_state.conversation:
            with st.spinner("Generating SOAP note..."):
                response = requests.post(f"{BACKEND_URL}/generate_soap/")
                if response.ok:
                    soap_note = response.json().get("soap_note", "")
                    st.session_state.soap_note = soap_note

with col_soap2:
    if hasattr(st.session_state, 'soap_note'):
        st.text_area("📋 Generated SOAP Note", value=st.session_state.soap_note, height=300)

# Process audio if recorded
if audio_bytes:
    with st.spinner("Transcribing audio..."):
        # Save audio temporarily
        with open("temp_audio.wav", "wb") as f:
            f.write(audio_bytes)

        # Send to backend for transcription
        with open("temp_audio.wav", "rb") as f:
            files = {"audio": ("temp_audio.wav", f, "audio/wav")}
            response = requests.post(f"{BACKEND_URL}/analyze/", files=files)

        if response.ok:
            transcribed_text = response.json().get("transcribed_text", "")
            if transcribed_text:
                # Add to conversation
                data = {"speaker": speaker, "text": transcribed_text}
                response = requests.post(f"{BACKEND_URL}/add_speech/", data=data)
                if response.ok:
                    st.session_state.conversation = response.json()["conversation"]
                    st.session_state.current_speaker = "Patient" if speaker == "Doctor" else "Doctor"
                    st.rerun()
