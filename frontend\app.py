# frontend/app.py
import streamlit as st
import requests
import random
import time
from audio_recorder_streamlit import audio_recorder

st.set_page_config(page_title="🩺 SOAP Note Generator", layout="wide")

BACKEND_URL = "http://localhost:8000"

# Helper function for doctor responses
def generate_doctor_response(patient_message):
    """Generate doctor response based on patient's message"""
    doctor_responses = [
        "Can you tell me more about when this started?",
        "How would you rate the pain on a scale of 1 to 10?",
        "Have you taken any medication for this?",
        "Does anything make it better or worse?",
        "Any other symptoms you've noticed?",
        "Do you have any allergies I should know about?",
        "Let me examine that area. How does this feel?",
        "Based on what you're telling me, I think we should run some tests."
    ]
    return random.choice(doctor_responses)

st.title("🩺 SOAP Note Generator")
st.markdown("**🤒 Patient-Doctor Consultation System**")
st.markdown("---")

# Initialize session state
if 'conversation' not in st.session_state:
    st.session_state.conversation = []
if 'consultation_active' not in st.session_state:
    st.session_state.consultation_active = False

# Main interface
col1, col2 = st.columns([1, 1])

with col1:
    st.subheader("🤒 Patient Interface")

    if not st.session_state.consultation_active:
        st.info("👋 Welcome! Press the button below to start your consultation")

        if st.button("🎤 Start Consultation", type="primary", use_container_width=True):
            st.session_state.consultation_active = True
            st.session_state.conversation = []
            st.rerun()

    else:
        st.success("🔴 Consultation in Progress")
        st.markdown("**Speak when you hear the beep, doctor will respond**")

        # Patient audio input
        audio_bytes = audio_recorder(
            text="🎤 Patient - Speak Now",
            recording_color="#e74c3c",
            neutral_color="#2ecc71",
            icon_name="microphone",
            icon_size="3x",
            key=f"patient_recorder_{len(st.session_state.conversation)}"
        )

        # Process patient audio
        if audio_bytes:
            with st.spinner("🎧 Processing your speech..."):
                # Simulate transcription (replace with actual backend call)
                patient_text = f"Patient spoke at {time.strftime('%H:%M:%S')}"

                # Add patient speech to conversation
                st.session_state.conversation.append({
                    "speaker": "Patient",
                    "text": patient_text,
                    "time": time.strftime('%H:%M:%S')
                })

                # Generate doctor response
                doctor_response = generate_doctor_response(patient_text)
                st.session_state.conversation.append({
                    "speaker": "Doctor",
                    "text": doctor_response,
                    "time": time.strftime('%H:%M:%S')
                })

                st.rerun()

        # Stop consultation
        if st.button("🛑 End Consultation", type="secondary"):
            st.session_state.consultation_active = False
            # Auto-generate SOAP note
            if st.session_state.conversation:
                st.session_state.soap_generated = True
            st.rerun()

with col2:
    st.subheader("💬 Live Consultation")

    # Display conversation in real-time
    if st.session_state.conversation:
        conversation_container = st.container()
        with conversation_container:
            for i, item in enumerate(st.session_state.conversation):
                speaker_emoji = "👨‍⚕️" if item["speaker"] == "Doctor" else "🤒"
                time_stamp = item.get("time", "")

                # Different styling for each speaker
                if item["speaker"] == "Doctor":
                    st.markdown(f"""
                    <div style="background-color: #e3f2fd; padding: 15px; border-radius: 15px; margin: 10px 0; border-left: 5px solid #2196F3;">
                        <strong>{speaker_emoji} Doctor [{time_stamp}]:</strong><br>
                        <em>{item['text']}</em>
                    </div>
                    """, unsafe_allow_html=True)
                else:
                    st.markdown(f"""
                    <div style="background-color: #f3e5f5; padding: 15px; border-radius: 15px; margin: 10px 0; border-left: 5px solid #9C27B0;">
                        <strong>{speaker_emoji} Patient [{time_stamp}]:</strong><br>
                        <em>{item['text']}</em>
                    </div>
                    """, unsafe_allow_html=True)
    else:
        st.info("🎤 Start consultation to see live conversation here!")

# Auto-generate SOAP note when consultation ends
if hasattr(st.session_state, 'soap_generated') and st.session_state.soap_generated:
    st.markdown("---")
    st.subheader("📋 Consultation Summary")

    with st.spinner("Generating SOAP note..."):
        # Format conversation for SOAP generation
        formatted_conversation = "\n".join([f"{item['speaker']}: {item['text']}" for item in st.session_state.conversation])

        # Generate SOAP note
        soap_note = f"""
**SOAP Note - Generated Automatically**

**S – Subjective:**
Patient reported symptoms and concerns during consultation.

**O – Objective:**
Clinical observations and examination findings.

**A – Assessment:**
Medical assessment based on patient consultation.

**P – Plan:**
Recommended treatment plan and follow-up.

**Conversation Log:**
{formatted_conversation}
"""

        st.text_area("📋 Generated SOAP Note", value=soap_note, height=400)

        # Reset for next consultation
        if st.button("🔄 Start New Consultation"):
            st.session_state.conversation = []
            st.session_state.consultation_active = False
            st.session_state.soap_generated = False
            st.rerun()


