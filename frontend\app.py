# frontend/app.py
import streamlit as st
import requests
import random
import time
from audio_recorder_streamlit import audio_recorder

st.set_page_config(page_title="🩺 SOAP Note Generator", layout="wide")

BACKEND_URL = "http://localhost:8000"

# Helper function for doctor responses
def generate_doctor_response(patient_message):
    """Generate doctor response based on patient's message"""
    doctor_responses = [
        "Hello! Can you tell me more about when this started?",
        "How would you rate the pain on a scale of 1 to 10?",
        "Have you taken any medication for this?",
        "Does anything make it better or worse?",
        "Any other symptoms you've noticed?",
        "Do you have any allergies I should know about?",
        "Let me examine that area. How does this feel?",
        "Based on what you're telling me, I think we should run some tests.",
        "Thank you for that information. What else can you tell me?"
    ]
    return random.choice(doctor_responses)

st.title("🩺 SOAP Note Generator")
st.markdown("**🤒 Patient-Doctor Consultation System**")
st.markdown("---")

# Initialize session state
if 'conversation' not in st.session_state:
    st.session_state.conversation = []
if 'consultation_active' not in st.session_state:
    st.session_state.consultation_active = False

# Main interface
col1, col2 = st.columns([1, 1])

with col1:
    st.subheader("🎤 Communication Panel")

    if not st.session_state.consultation_active:
        st.info("👋 Welcome! Press the button below to start consultation")

        if st.button("🎤 Start Consultation", type="primary", use_container_width=True):
            st.session_state.consultation_active = True
            st.session_state.conversation = []
            st.rerun()

    else:
        st.success("🔴 Live Consultation Active")

        # Two-way communication
        col_patient, col_doctor = st.columns(2)

        with col_patient:
            st.markdown("**🤒 Patient**")
            patient_audio = audio_recorder(
                text="Patient Speak",
                recording_color="#e74c3c",
                neutral_color="#9C27B0",
                icon_name="microphone",
                icon_size="2x",
                key=f"patient_{len(st.session_state.conversation)}"
            )

            # Process patient audio
            if patient_audio:
                with st.spinner("Processing patient..."):
                    patient_text = "I have been feeling unwell with headaches and fatigue"

                    st.session_state.conversation.append({
                        "speaker": "Patient",
                        "text": patient_text,
                        "time": time.strftime('%H:%M:%S')
                    })
                    st.rerun()

        with col_doctor:
            st.markdown("**👨‍⚕️ Doctor**")
            doctor_audio = audio_recorder(
                text="Doctor Speak",
                recording_color="#2196F3",
                neutral_color="#2196F3",
                icon_name="microphone",
                icon_size="2x",
                key=f"doctor_{len(st.session_state.conversation)}"
            )

            # Process doctor audio
            if doctor_audio:
                with st.spinner("Processing doctor..."):
                    doctor_text = generate_doctor_response("patient_message")

                    st.session_state.conversation.append({
                        "speaker": "Doctor",
                        "text": doctor_text,
                        "time": time.strftime('%H:%M:%S')
                    })
                    st.rerun()

        # Stop consultation
        st.markdown("---")
        if st.button("🛑 End Consultation", type="secondary", use_container_width=True):
            st.session_state.consultation_active = False
            if st.session_state.conversation:
                st.session_state.soap_generated = True
            st.rerun()

with col2:
    st.subheader("💬 Live Conversation & SOAP Note")

    # Display conversation in real-time
    if st.session_state.conversation:
        st.markdown("**🗣️ Conversation Log:**")
        conversation_container = st.container()
        with conversation_container:
            for i, item in enumerate(st.session_state.conversation):
                speaker_emoji = "👨‍⚕️" if item["speaker"] == "Doctor" else "🤒"
                time_stamp = item.get("time", "")

                # Clean conversation display
                if item["speaker"] == "Doctor":
                    st.markdown(f"""
                    <div style="background-color: #e8f4fd; padding: 12px; border-radius: 10px; margin: 8px 0; border-left: 4px solid #2196F3;">
                        <strong style="color: #1976D2;">{speaker_emoji} Doctor [{time_stamp}]:</strong><br>
                        <span style="color: #333;">{item['text']}</span>
                    </div>
                    """, unsafe_allow_html=True)
                else:
                    st.markdown(f"""
                    <div style="background-color: #fce4ec; padding: 12px; border-radius: 10px; margin: 8px 0; border-left: 4px solid #9C27B0;">
                        <strong style="color: #7B1FA2;">{speaker_emoji} Patient [{time_stamp}]:</strong><br>
                        <span style="color: #333;">{item['text']}</span>
                    </div>
                    """, unsafe_allow_html=True)

        # Auto-generate SOAP note in real-time
        st.markdown("---")
        st.markdown("**📋 Live SOAP Note:**")
        formatted_conversation = "\n".join([f"{item['speaker']}: {item['text']}" for item in st.session_state.conversation])

        live_soap = f"""
**S – Subjective:**
Patient symptoms and concerns from conversation.

**O – Objective:**
Clinical observations during consultation.

**A – Assessment:**
Medical evaluation based on patient interaction.

**P – Plan:**
Treatment recommendations and follow-up.

**Conversation Summary:**
{formatted_conversation}
"""
        st.text_area("📋 SOAP Note", value=live_soap, height=300, key="live_soap")

    else:
        st.info("🎤 Start consultation to see live conversation and SOAP note here!")

# Final SOAP note when consultation ends
if hasattr(st.session_state, 'soap_generated') and st.session_state.soap_generated:
    st.markdown("---")
    st.subheader("📋 Final Consultation Summary")

    # Format conversation for final SOAP generation
    formatted_conversation = "\n".join([f"{item['speaker']}: {item['text']}" for item in st.session_state.conversation])

    # Generate final SOAP note
    final_soap = f"""
**SOAP Note - Consultation Complete**

**S – Subjective:**
Patient reported symptoms and concerns during consultation.

**O – Objective:**
Clinical observations and examination findings.

**A – Assessment:**
Medical assessment based on patient consultation.

**P – Plan:**
Recommended treatment plan and follow-up.

**Full Conversation Log:**
{formatted_conversation}
"""

    st.text_area("📋 Final SOAP Note", value=final_soap, height=400)

    # Reset for next consultation
    if st.button("🔄 Start New Consultation"):
        st.session_state.conversation = []
        st.session_state.consultation_active = False
        st.session_state.soap_generated = False
        st.rerun()


