# frontend/app.py
import streamlit as st
import requests
import random
from audio_recorder_streamlit import audio_recorder

st.set_page_config(page_title="🩺 SOAP Note Generator", layout="wide")

BACKEND_URL = "http://localhost:8000"

# Helper function for AI responses
def generate_ai_response(speaker_type, last_message):
    """Generate AI response for patient based on doctor's question"""
    if speaker_type == "Patient":
        # Simple patient responses - you can enhance this
        patient_responses = [
            "Yes, I've been experiencing that for about a week now.",
            "It started gradually and has been getting worse.",
            "The pain is about 6 out of 10.",
            "I haven't taken any medication for it yet.",
            "It happens mostly in the morning.",
            "No, I don't have any allergies.",
            "I feel a bit tired lately.",
            "The symptoms are affecting my daily activities."
        ]
        return random.choice(patient_responses)
    return None

st.title("🩺 SOAP Note Generator")
st.markdown("**Real-time Doctor-Patient Live Conversation**")

# Initialize session state
if 'conversation' not in st.session_state:
    st.session_state.conversation = []
if 'is_listening' not in st.session_state:
    st.session_state.is_listening = False
if 'current_speaker' not in st.session_state:
    st.session_state.current_speaker = "Doctor"

# Create layout
col1, col2 = st.columns([1, 1])

with col1:
    st.subheader("🎤 Live Meeting Room")

    # Meeting controls
    col_start, col_stop = st.columns(2)

    with col_start:
        if st.button("🟢 Start Live Conversation", type="primary"):
            st.session_state.is_listening = True
            st.rerun()

    with col_stop:
        if st.button("🔴 Stop Conversation"):
            st.session_state.is_listening = False
            st.rerun()

    # Status indicator
    if st.session_state.is_listening:
        st.success("🔴 LIVE - Conversation in progress...")
        st.markdown("**Current Speaker:** " + st.session_state.current_speaker)
    else:
        st.info("⚪ Ready to start conversation")

    # Voice input section
    if st.session_state.is_listening:
        st.markdown("---")
        st.markdown(f"**🎤 {st.session_state.current_speaker} - Speak now:**")

        # Audio recorder for current speaker
        audio_bytes = audio_recorder(
            text=f"{st.session_state.current_speaker} speaking...",
            recording_color="#e74c3c",
            neutral_color="#2ecc71",
            icon_name="microphone",
            icon_size="2x",
            key=f"recorder_{st.session_state.current_speaker}_{len(st.session_state.conversation)}"
        )

        # Process audio immediately when recorded
        if audio_bytes:
            with st.spinner(f"Processing {st.session_state.current_speaker}'s speech..."):
                try:
                    # Save and transcribe
                    with open("temp_audio.wav", "wb") as f:
                        f.write(audio_bytes)

                    with open("temp_audio.wav", "rb") as f:
                        files = {"audio": ("temp_audio.wav", f, "audio/wav")}
                        response = requests.post(f"{BACKEND_URL}/analyze/", files=files)

                    if response.ok:
                        result = response.json()
                        transcribed_text = result.get("transcribed_text", "")

                        if transcribed_text:
                            # Add to local conversation
                            st.session_state.conversation.append({
                                "speaker": st.session_state.current_speaker,
                                "text": transcribed_text
                            })

                            # Switch speaker automatically
                            st.session_state.current_speaker = "Patient" if st.session_state.current_speaker == "Doctor" else "Doctor"

                            # Generate AI response if needed
                            if st.session_state.current_speaker == "Patient":
                                # Generate patient response
                                ai_response = generate_ai_response("Patient", transcribed_text)
                                if ai_response:
                                    st.session_state.conversation.append({
                                        "speaker": "Patient",
                                        "text": ai_response
                                    })
                                    st.session_state.current_speaker = "Doctor"

                            st.rerun()
                except Exception as e:
                    st.error(f"Backend connection error: {e}")
                    st.info("Using offline mode - add text manually below")

    # Manual input for offline mode
    st.markdown("---")
    st.markdown("**Manual Input (Offline Mode):**")
    manual_speaker = st.selectbox("Speaker:", ["Doctor", "Patient"])
    manual_text = st.text_area("Enter speech:", height=100)

    if st.button("➕ Add to Conversation") and manual_text.strip():
        st.session_state.conversation.append({
            "speaker": manual_speaker,
            "text": manual_text.strip()
        })
        st.rerun()

with col2:
    st.subheader("💬 Live Conversation")

    # Display conversation
    if st.session_state.conversation:
        conversation_container = st.container()
        with conversation_container:
            for i, item in enumerate(st.session_state.conversation):
                speaker_emoji = "👨‍⚕️" if item["speaker"] == "Doctor" else "🤒"

                # Different styling for each speaker
                if item["speaker"] == "Doctor":
                    st.markdown(f"""
                    <div style="background-color: #e3f2fd; padding: 10px; border-radius: 10px; margin: 5px 0;">
                        <strong>{speaker_emoji} {item['speaker']}:</strong><br>
                        {item['text']}
                    </div>
                    """, unsafe_allow_html=True)
                else:
                    st.markdown(f"""
                    <div style="background-color: #f3e5f5; padding: 10px; border-radius: 10px; margin: 5px 0;">
                        <strong>{speaker_emoji} {item['speaker']}:</strong><br>
                        {item['text']}
                    </div>
                    """, unsafe_allow_html=True)
    else:
        st.info("🎤 Start the conversation to see live dialogue here!")

# SOAP Note Generation
st.markdown("---")
col_soap1, col_soap2 = st.columns([1, 2])

with col_soap1:
    if st.button("📋 Generate SOAP Note", type="primary"):
        if st.session_state.conversation:
            with st.spinner("Generating SOAP note..."):
                try:
                    # Format conversation for SOAP generation
                    formatted_conversation = "\n".join([f"{item['speaker']}: {item['text']}" for item in st.session_state.conversation])

                    # Try backend first
                    response = requests.post(f"{BACKEND_URL}/generate_soap/")
                    if response.ok:
                        soap_note = response.json().get("soap_note", "")
                        st.session_state.soap_note = soap_note
                    else:
                        # Fallback to simple formatting
                        st.session_state.soap_note = f"""
**SOAP Note Generated from Conversation:**

**S – Subjective:**
{formatted_conversation}

**O – Objective:**
[Physical examination findings to be added]

**A – Assessment:**
[Clinical assessment based on conversation]

**P – Plan:**
[Treatment plan to be determined]
"""
                except Exception as e:
                    st.error(f"Error generating SOAP note: {e}")

with col_soap2:
    if hasattr(st.session_state, 'soap_note'):
        st.text_area("📋 Generated SOAP Note", value=st.session_state.soap_note, height=300)


