# frontend/app.py
import streamlit as st
import requests
import random
import time
from audio_recorder_streamlit import audio_recorder

st.set_page_config(page_title="🩺 SOAP Note Generator", layout="wide")

BACKEND_URL = "http://localhost:8000"

# LLM Doctor Response Function
def generate_doctor_response(patient_message):
    """Generate real doctor response using LLM"""
    try:
        # Try backend LLM first
        response = requests.post(f"{BACKEND_URL}/chat/",
                               json={"message": patient_message},
                               timeout=10)
        if response.ok:
            return response.json().get("response", "I understand. Can you tell me more?")
    except:
        pass

    # Fallback: Simple medical responses based on keywords
    patient_lower = patient_message.lower()

    if "headache" in patient_lower or "head" in patient_lower:
        return "I see you're experiencing headaches. How long have you been having them? Are they constant or do they come and go?"
    elif "pain" in patient_lower:
        return "Can you describe the pain? Is it sharp, dull, throbbing? On a scale of 1-10, how would you rate it?"
    elif "fever" in patient_lower or "temperature" in patient_lower:
        return "Have you taken your temperature? Any other symptoms like chills, body aches, or fatigue?"
    elif "cough" in patient_lower:
        return "How long have you had this cough? Is it dry or are you bringing up any phlegm?"
    elif "stomach" in patient_lower or "nausea" in patient_lower:
        return "Are you experiencing any nausea or vomiting? When did these stomach issues start?"
    elif "hello" in patient_lower or "hi" in patient_lower:
        return "Hello! I'm Dr. Smith. What brings you in today? Please tell me about your symptoms."
    else:
        return "I understand. Can you provide more details about your symptoms? When did this start?"

st.title("🩺 SOAP Note Generator")
st.markdown("**🤒 Patient-Doctor Consultation System**")
st.markdown("---")

# Initialize session state
if 'conversation' not in st.session_state:
    st.session_state.conversation = []
if 'consultation_active' not in st.session_state:
    st.session_state.consultation_active = False

# Main interface
col1, col2 = st.columns([1, 1])

with col1:
    st.subheader("🎤 Communication Panel")

    if not st.session_state.consultation_active:
        st.info("👋 Welcome! Press the button below to start consultation")

        if st.button("🎤 Start Consultation", type="primary", use_container_width=True):
            st.session_state.consultation_active = True
            st.session_state.conversation = []
            st.rerun()

    else:
        st.success("🔴 Live Consultation Active")

        # Patient speaks, Doctor responds with LLM
        st.markdown("**🤒 Patient - Speak to Doctor**")

        patient_audio = audio_recorder(
            text="🎤 Patient - Tell Doctor Your Symptoms",
            recording_color="#e74c3c",
            neutral_color="#9C27B0",
            icon_name="microphone",
            icon_size="3x",
            key=f"patient_only_{len(st.session_state.conversation)}"
        )

        # Process patient audio and get LLM doctor response
        if patient_audio:
            with st.spinner("🎧 Processing your speech and getting doctor response..."):
                # Simulate patient speech (replace with real transcription)
                patient_text = "Hello doctor, I have been having severe headaches for the past 3 days"

                # Add patient speech to conversation
                st.session_state.conversation.append({
                    "speaker": "Patient",
                    "text": patient_text,
                    "time": time.strftime('%H:%M:%S')
                })

                # Get LLM doctor response
                doctor_response = generate_doctor_response(patient_text)

                # Add doctor response to conversation
                st.session_state.conversation.append({
                    "speaker": "Doctor",
                    "text": doctor_response,
                    "time": time.strftime('%H:%M:%S')
                })

                st.success("✅ Doctor responded!")
                st.rerun()

        # Stop consultation
        st.markdown("---")
        if st.button("🛑 End Consultation", type="secondary", use_container_width=True):
            st.session_state.consultation_active = False
            if st.session_state.conversation:
                st.session_state.soap_generated = True
            st.rerun()

with col2:
    st.subheader("💬 Live Conversation & SOAP Note")

    # Display conversation in real-time
    if st.session_state.conversation:
        st.markdown("**🗣️ Conversation Log:**")
        conversation_container = st.container()
        with conversation_container:
            for i, item in enumerate(st.session_state.conversation):
                speaker_emoji = "👨‍⚕️" if item["speaker"] == "Doctor" else "🤒"
                time_stamp = item.get("time", "")

                # Clean conversation display
                if item["speaker"] == "Doctor":
                    st.markdown(f"""
                    <div style="background-color: #e8f4fd; padding: 12px; border-radius: 10px; margin: 8px 0; border-left: 4px solid #2196F3;">
                        <strong style="color: #1976D2;">{speaker_emoji} Doctor [{time_stamp}]:</strong><br>
                        <span style="color: #333;">{item['text']}</span>
                    </div>
                    """, unsafe_allow_html=True)
                else:
                    st.markdown(f"""
                    <div style="background-color: #fce4ec; padding: 12px; border-radius: 10px; margin: 8px 0; border-left: 4px solid #9C27B0;">
                        <strong style="color: #7B1FA2;">{speaker_emoji} Patient [{time_stamp}]:</strong><br>
                        <span style="color: #333;">{item['text']}</span>
                    </div>
                    """, unsafe_allow_html=True)

        # Auto-generate SOAP note in real-time
        st.markdown("---")
        st.markdown("**📋 Live SOAP Note:**")
        formatted_conversation = "\n".join([f"{item['speaker']}: {item['text']}" for item in st.session_state.conversation])

        live_soap = f"""
**S – Subjective:**
Patient symptoms and concerns from conversation.

**O – Objective:**
Clinical observations during consultation.

**A – Assessment:**
Medical evaluation based on patient interaction.

**P – Plan:**
Treatment recommendations and follow-up.

**Conversation Summary:**
{formatted_conversation}
"""
        st.text_area("📋 SOAP Note", value=live_soap, height=300, key="live_soap")

    else:
        st.info("🎤 Start consultation to see live conversation and SOAP note here!")

# Final SOAP note when consultation ends
if hasattr(st.session_state, 'soap_generated') and st.session_state.soap_generated:
    st.markdown("---")
    st.subheader("📋 Final Consultation Summary")

    # Format conversation for final SOAP generation
    formatted_conversation = "\n".join([f"{item['speaker']}: {item['text']}" for item in st.session_state.conversation])

    # Generate final SOAP note
    final_soap = f"""
**SOAP Note - Consultation Complete**

**S – Subjective:**
Patient reported symptoms and concerns during consultation.

**O – Objective:**
Clinical observations and examination findings.

**A – Assessment:**
Medical assessment based on patient consultation.

**P – Plan:**
Recommended treatment plan and follow-up.

**Full Conversation Log:**
{formatted_conversation}
"""

    st.text_area("📋 Final SOAP Note", value=final_soap, height=400)

    # Reset for next consultation
    if st.button("🔄 Start New Consultation"):
        st.session_state.conversation = []
        st.session_state.consultation_active = False
        st.session_state.soap_generated = False
        st.rerun()


