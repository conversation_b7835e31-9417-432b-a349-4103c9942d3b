altair-4.2.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
altair-4.2.2.dist-info/LICENSE,sha256=MvF85LRhteZoWExdbH9i4AD03ei0RnKFg6nnMEDsqPg,1501
altair-4.2.2.dist-info/METADATA,sha256=qYg9Vxh4K7Fjaxy8_R4gNigDJGy0F6sWu6MkJiFPZ1E,13748
altair-4.2.2.dist-info/RECORD,,
altair-4.2.2.dist-info/WHEEL,sha256=G16H4A3IeoQmnOrYV4ueZGKSjhipXx8zc8nu9FGlvMA,92
altair-4.2.2.dist-info/top_level.txt,sha256=JMBcunbUG0vYB5joP_RPeeOVAvx4F4HGmQ7B-dlv6qI,7
altair/__init__.py,sha256=wc7URtMYb68JpELMTRJlgkic4AZKlKTzvy3dqFKPxiw,269
altair/__pycache__/__init__.cpython-312.pyc,,
altair/__pycache__/_magics.cpython-312.pyc,,
altair/__pycache__/datasets.cpython-312.pyc,,
altair/_magics.py,sha256=ze4PT_Uu0vdsS9ve5OfZ-ZDQw19HaONqFGwwTGqVHZM,5203
altair/datasets.py,sha256=IQMisnfDA0QRNQmplMRY2lFJiHAf3ajY5MW3_kw2ExU,645
altair/examples/__init__.py,sha256=M3X7GJu5K7hu2KZCUWJmcy90ZeRgrcQ78jACsH0Wx3Y,558
altair/examples/__pycache__/__init__.cpython-312.pyc,,
altair/examples/__pycache__/airport_connections.cpython-312.pyc,,
altair/examples/__pycache__/airports.cpython-312.pyc,,
altair/examples/__pycache__/airports_count.cpython-312.pyc,,
altair/examples/__pycache__/anscombe_plot.cpython-312.pyc,,
altair/examples/__pycache__/area_chart_gradient.cpython-312.pyc,,
altair/examples/__pycache__/bar_and_line_with_dual_axis.cpython-312.pyc,,
altair/examples/__pycache__/bar_chart_horizontal.cpython-312.pyc,,
altair/examples/__pycache__/bar_chart_sorted.cpython-312.pyc,,
altair/examples/__pycache__/bar_chart_trellis_compact.cpython-312.pyc,,
altair/examples/__pycache__/bar_chart_with_highlighted_bar.cpython-312.pyc,,
altair/examples/__pycache__/bar_chart_with_highlighted_segment.cpython-312.pyc,,
altair/examples/__pycache__/bar_chart_with_labels.cpython-312.pyc,,
altair/examples/__pycache__/bar_chart_with_mean_line.cpython-312.pyc,,
altair/examples/__pycache__/bar_chart_with_negatives.cpython-312.pyc,,
altair/examples/__pycache__/bar_rounded.cpython-312.pyc,,
altair/examples/__pycache__/bar_with_rolling_mean.cpython-312.pyc,,
altair/examples/__pycache__/beckers_barley_trellis_plot.cpython-312.pyc,,
altair/examples/__pycache__/beckers_barley_wrapped_facet.cpython-312.pyc,,
altair/examples/__pycache__/binned_heatmap.cpython-312.pyc,,
altair/examples/__pycache__/binned_scatterplot.cpython-312.pyc,,
altair/examples/__pycache__/boxplot.cpython-312.pyc,,
altair/examples/__pycache__/bubble_plot.cpython-312.pyc,,
altair/examples/__pycache__/bump_chart.cpython-312.pyc,,
altair/examples/__pycache__/candlestick_chart.cpython-312.pyc,,
altair/examples/__pycache__/choropleth.cpython-312.pyc,,
altair/examples/__pycache__/choropleth_repeat.cpython-312.pyc,,
altair/examples/__pycache__/co2_concentration.cpython-312.pyc,,
altair/examples/__pycache__/comet_chart.cpython-312.pyc,,
altair/examples/__pycache__/connected_scatterplot.cpython-312.pyc,,
altair/examples/__pycache__/cumulative_count_chart.cpython-312.pyc,,
altair/examples/__pycache__/cumulative_wiki_donations.cpython-312.pyc,,
altair/examples/__pycache__/density_facet.cpython-312.pyc,,
altair/examples/__pycache__/density_stack.cpython-312.pyc,,
altair/examples/__pycache__/diverging_stacked_bar_chart.cpython-312.pyc,,
altair/examples/__pycache__/donut_chart.cpython-312.pyc,,
altair/examples/__pycache__/dot_dash_plot.cpython-312.pyc,,
altair/examples/__pycache__/errorbars_with_ci.cpython-312.pyc,,
altair/examples/__pycache__/errorbars_with_std.cpython-312.pyc,,
altair/examples/__pycache__/falkensee.cpython-312.pyc,,
altair/examples/__pycache__/filled_step_chart.cpython-312.pyc,,
altair/examples/__pycache__/gantt_chart.cpython-312.pyc,,
altair/examples/__pycache__/gapminder_bubble_plot.cpython-312.pyc,,
altair/examples/__pycache__/grouped_bar_chart.cpython-312.pyc,,
altair/examples/__pycache__/grouped_bar_chart_horizontal.cpython-312.pyc,,
altair/examples/__pycache__/grouped_bar_chart_with_error_bars.cpython-312.pyc,,
altair/examples/__pycache__/hexbins.cpython-312.pyc,,
altair/examples/__pycache__/histogram_responsive.cpython-312.pyc,,
altair/examples/__pycache__/histogram_with_a_global_mean_overlay.cpython-312.pyc,,
altair/examples/__pycache__/horizon_graph.cpython-312.pyc,,
altair/examples/__pycache__/horizontal_stacked_bar_chart.cpython-312.pyc,,
altair/examples/__pycache__/image_tooltip.cpython-312.pyc,,
altair/examples/__pycache__/interactive_brush.cpython-312.pyc,,
altair/examples/__pycache__/interactive_cross_highlight.cpython-312.pyc,,
altair/examples/__pycache__/interactive_layered_crossfilter.cpython-312.pyc,,
altair/examples/__pycache__/interactive_legend.cpython-312.pyc,,
altair/examples/__pycache__/interactive_scatter_plot.cpython-312.pyc,,
altair/examples/__pycache__/interval_selection.cpython-312.pyc,,
altair/examples/__pycache__/iowa_electricity.cpython-312.pyc,,
altair/examples/__pycache__/isotype.cpython-312.pyc,,
altair/examples/__pycache__/isotype_emoji.cpython-312.pyc,,
altair/examples/__pycache__/isotype_grid.cpython-312.pyc,,
altair/examples/__pycache__/layer_line_color_rule.cpython-312.pyc,,
altair/examples/__pycache__/layered_area_chart.cpython-312.pyc,,
altair/examples/__pycache__/layered_bar_chart.cpython-312.pyc,,
altair/examples/__pycache__/layered_chart_bar_mark.cpython-312.pyc,,
altair/examples/__pycache__/layered_chart_with_dual_axis.cpython-312.pyc,,
altair/examples/__pycache__/layered_heatmap_text.cpython-312.pyc,,
altair/examples/__pycache__/layered_histogram.cpython-312.pyc,,
altair/examples/__pycache__/line_chart_with_color_datum.cpython-312.pyc,,
altair/examples/__pycache__/line_chart_with_cumsum.cpython-312.pyc,,
altair/examples/__pycache__/line_chart_with_datum.cpython-312.pyc,,
altair/examples/__pycache__/line_chart_with_generator.cpython-312.pyc,,
altair/examples/__pycache__/line_chart_with_points.cpython-312.pyc,,
altair/examples/__pycache__/line_percent.cpython-312.pyc,,
altair/examples/__pycache__/line_with_ci.cpython-312.pyc,,
altair/examples/__pycache__/line_with_log_scale.cpython-312.pyc,,
altair/examples/__pycache__/london_tube.cpython-312.pyc,,
altair/examples/__pycache__/multi_series_line.cpython-312.pyc,,
altair/examples/__pycache__/multifeature_scatter_plot.cpython-312.pyc,,
altair/examples/__pycache__/multiline_highlight.cpython-312.pyc,,
altair/examples/__pycache__/multiline_tooltip.cpython-312.pyc,,
altair/examples/__pycache__/multiple_interactions.cpython-312.pyc,,
altair/examples/__pycache__/multiple_marks.cpython-312.pyc,,
altair/examples/__pycache__/natural_disasters.cpython-312.pyc,,
altair/examples/__pycache__/normalized_stacked_area_chart.cpython-312.pyc,,
altair/examples/__pycache__/normalized_stacked_bar_chart.cpython-312.pyc,,
altair/examples/__pycache__/normed_parallel_coordinates.cpython-312.pyc,,
altair/examples/__pycache__/one_dot_per_zipcode.cpython-312.pyc,,
altair/examples/__pycache__/pacman_chart.cpython-312.pyc,,
altair/examples/__pycache__/parallel_coordinates.cpython-312.pyc,,
altair/examples/__pycache__/percentage_of_total.cpython-312.pyc,,
altair/examples/__pycache__/pie_chart.cpython-312.pyc,,
altair/examples/__pycache__/pie_chart_with_labels.cpython-312.pyc,,
altair/examples/__pycache__/poly_fit_regression.cpython-312.pyc,,
altair/examples/__pycache__/pyramid.cpython-312.pyc,,
altair/examples/__pycache__/radial_chart.cpython-312.pyc,,
altair/examples/__pycache__/ranged_dot_plot.cpython-312.pyc,,
altair/examples/__pycache__/ridgeline_plot.cpython-312.pyc,,
altair/examples/__pycache__/scatter_href.cpython-312.pyc,,
altair/examples/__pycache__/scatter_linked_brush.cpython-312.pyc,,
altair/examples/__pycache__/scatter_linked_table.cpython-312.pyc,,
altair/examples/__pycache__/scatter_marginal_hist.cpython-312.pyc,,
altair/examples/__pycache__/scatter_matrix.cpython-312.pyc,,
altair/examples/__pycache__/scatter_qq.cpython-312.pyc,,
altair/examples/__pycache__/scatter_tooltips.cpython-312.pyc,,
altair/examples/__pycache__/scatter_with_histogram.cpython-312.pyc,,
altair/examples/__pycache__/scatter_with_labels.cpython-312.pyc,,
altair/examples/__pycache__/scatter_with_layered_histogram.cpython-312.pyc,,
altair/examples/__pycache__/scatter_with_loess.cpython-312.pyc,,
altair/examples/__pycache__/scatter_with_minimap.cpython-312.pyc,,
altair/examples/__pycache__/scatter_with_rolling_mean.cpython-312.pyc,,
altair/examples/__pycache__/seattle_weather_interactive.cpython-312.pyc,,
altair/examples/__pycache__/select_detail.cpython-312.pyc,,
altair/examples/__pycache__/select_mark_area.cpython-312.pyc,,
altair/examples/__pycache__/selection_histogram.cpython-312.pyc,,
altair/examples/__pycache__/selection_layer_bar_month.cpython-312.pyc,,
altair/examples/__pycache__/simple_bar_chart.cpython-312.pyc,,
altair/examples/__pycache__/simple_heatmap.cpython-312.pyc,,
altair/examples/__pycache__/simple_histogram.cpython-312.pyc,,
altair/examples/__pycache__/simple_line_chart.cpython-312.pyc,,
altair/examples/__pycache__/simple_scatter_with_errorbars.cpython-312.pyc,,
altair/examples/__pycache__/simple_stacked_area_chart.cpython-312.pyc,,
altair/examples/__pycache__/slope_graph.cpython-312.pyc,,
altair/examples/__pycache__/sorted_error_bars_with_ci.cpython-312.pyc,,
altair/examples/__pycache__/stacked_bar_chart.cpython-312.pyc,,
altair/examples/__pycache__/stacked_bar_chart_sorted_segments.cpython-312.pyc,,
altair/examples/__pycache__/stacked_bar_chart_with_text.cpython-312.pyc,,
altair/examples/__pycache__/stem_and_leaf.cpython-312.pyc,,
altair/examples/__pycache__/step_chart.cpython-312.pyc,,
altair/examples/__pycache__/streamgraph.cpython-312.pyc,,
altair/examples/__pycache__/strip_plot.cpython-312.pyc,,
altair/examples/__pycache__/stripplot.cpython-312.pyc,,
altair/examples/__pycache__/table_bubble_plot_github.cpython-312.pyc,,
altair/examples/__pycache__/top_k_items.cpython-312.pyc,,
altair/examples/__pycache__/top_k_letters.cpython-312.pyc,,
altair/examples/__pycache__/top_k_with_others.cpython-312.pyc,,
altair/examples/__pycache__/trail_marker.cpython-312.pyc,,
altair/examples/__pycache__/trellis_area.cpython-312.pyc,,
altair/examples/__pycache__/trellis_area_sort_array.cpython-312.pyc,,
altair/examples/__pycache__/trellis_histogram.cpython-312.pyc,,
altair/examples/__pycache__/trellis_scatter_plot.cpython-312.pyc,,
altair/examples/__pycache__/trellis_stacked_bar_chart.cpython-312.pyc,,
altair/examples/__pycache__/us_employment.cpython-312.pyc,,
altair/examples/__pycache__/us_incomebrackets_by_state_facet.cpython-312.pyc,,
altair/examples/__pycache__/us_population_over_time.cpython-312.pyc,,
altair/examples/__pycache__/us_population_over_time_facet.cpython-312.pyc,,
altair/examples/__pycache__/us_population_pyramid_over_time.cpython-312.pyc,,
altair/examples/__pycache__/us_state_capitals.cpython-312.pyc,,
altair/examples/__pycache__/violin_plot.cpython-312.pyc,,
altair/examples/__pycache__/weather_heatmap.cpython-312.pyc,,
altair/examples/__pycache__/wheat_wages.cpython-312.pyc,,
altair/examples/__pycache__/wilkinson-dot-plot.cpython-312.pyc,,
altair/examples/__pycache__/wind_vector_map.cpython-312.pyc,,
altair/examples/__pycache__/window_rank.cpython-312.pyc,,
altair/examples/__pycache__/world_map.cpython-312.pyc,,
altair/examples/__pycache__/world_projections.cpython-312.pyc,,
altair/examples/airport_connections.py,sha256=tohJON6VCWLbt_5zXgjCRL79U7DJhrtvAklr_gAhIw8,2038
altair/examples/airports.py,sha256=e6eaj4bnQed-1QzHUA-wmH0zDDkYoISA4ElLPkksvwM,754
altair/examples/airports_count.py,sha256=k3dkM6MOh1hOBWlxD6mOgwMm4LGSC_9_pnRhg_xXNt8,981
altair/examples/anscombe_plot.py,sha256=-YruFQG5GaWFJFLR0MToX2aaPezx42cpQwwvGK_m5GY,639
altair/examples/area_chart_gradient.py,sha256=8wTxc12HbD24NRYRiMPdCAmCcTdT65eBITIgflvi5Rs,779
altair/examples/bar_and_line_with_dual_axis.py,sha256=IHlOXqhueOaLUXsi9gBZ1Q3IXoJDTfNVahQpQmMClnQ,500
altair/examples/bar_chart_horizontal.py,sha256=pPrLkxR-V82A5DKTG9MQD8olGMAlNUS8ZeOSDYIjMZk,339
altair/examples/bar_chart_sorted.py,sha256=YFKh-mzZ2uYbmRddA_vQs2ESB6TQArTb8mjzZtewzP4,297
altair/examples/bar_chart_trellis_compact.py,sha256=uTuNtazaePQh31PwHeN6QUvC8e1bIDBsMIIlD5eH0Is,2141
altair/examples/bar_chart_with_highlighted_bar.py,sha256=KZeaGwocJW4AfiVJ6VL5_3p-lJaBzok4EZW4KQ62MG0,659
altair/examples/bar_chart_with_highlighted_segment.py,sha256=biAqU98UgJ_8Fg8bxe9HQznmtzRyoh8nMHMyV9n3YC4,696
altair/examples/bar_chart_with_labels.py,sha256=TMw473cVMhdXpw2aHsXegMTnqnneRhqOWno1JeAsNe4,516
altair/examples/bar_chart_with_mean_line.py,sha256=2xGpEt2L-CHv_dj0P3SOpyEftrPpnbDoXao6-Zl6n-Q,417
altair/examples/bar_chart_with_negatives.py,sha256=gELDIqTzqgc9czChfiuiFvzR_nC1BvVJ_I_Hal-jId4,525
altair/examples/bar_rounded.py,sha256=jfu2bMF8hw0XXRPnuti6AbyD7VWK-nOnPqkQu9AJW5k,398
altair/examples/bar_with_rolling_mean.py,sha256=l61KnSJ9claOPX0jB4p1Be6wkEnT4R1tPUjoecSML8Q,675
altair/examples/beckers_barley_trellis_plot.py,sha256=yBSMwsNy0aBIduHERKiKbLK0TQxF4EZ_G4nJ4bWGWtY,1183
altair/examples/beckers_barley_wrapped_facet.py,sha256=JKF1qJ6CTqViYyhG0PXTtuBMdEnTP_iFkEeY6ts6a1A,733
altair/examples/binned_heatmap.py,sha256=NTTUn7uF6dwz84yS5LuKHNbMHLiGbZh07T_exohW2Qg,435
altair/examples/binned_scatterplot.py,sha256=nXml10K_W7_omb8rRtXCYPnwNCE7uWyE_wPohB0N24A,352
altair/examples/boxplot.py,sha256=NyShVqkM_-Zm6OmxJrnltRmc4PLaXFMYqmAVXN4idgs,541
altair/examples/bubble_plot.py,sha256=11e5rq3fOGHXwtv3yRs2hc8VjZBcCiLbaOQaRB1JE6o,296
altair/examples/bump_chart.py,sha256=oNDSZA0tEg8HPBy8aHd6okYrRv2JOMUdbmJRknyRrwQ,763
altair/examples/candlestick_chart.py,sha256=tQUcEVtVI5Jv5QbLQ1wc18Fw27NJ5pEJxYcs6QWc9sA,1242
altair/examples/choropleth.py,sha256=QwI45kORaRNv4OUu3PeS-k-_J4tOATNMnuhs3MiYvRI,483
altair/examples/choropleth_repeat.py,sha256=XRfbRYh9L7WmcziyYusZz91eK96vDvejx8bIncwv9CM,692
altair/examples/co2_concentration.py,sha256=CPOSR0MyNhv3lLAkJPItuCO7megCYHmC4xv-zkySIYo,1690
altair/examples/comet_chart.py,sha256=G1de5F3cOAx7hUcsYZK25WmL7RKoRbF8uWEpD3vsBJQ,1450
altair/examples/connected_scatterplot.py,sha256=2iNWAAb2sdsPa5pM8ES6ZTuJEUQCUVvZ7efl0y5jDRk,726
altair/examples/cumulative_count_chart.py,sha256=0-0q7AU7FjNf9N1Ia0cO1hObFjKlTfFDVmDnckot188,475
altair/examples/cumulative_wiki_donations.py,sha256=uMV7wwz1IIwTOvvE0vKOSH8o8v5uTx4s__-gM4U5CTg,711
altair/examples/density_facet.py,sha256=06qZgw-mxN4wWZ1xrJCNEI3c8QKlOg2xnMy2GlUAZYY,635
altair/examples/density_stack.py,sha256=hXlDGMIKCO6opALtPD-mX4hmlP8qWKnCBOlgz54oXGE,1024
altair/examples/diverging_stacked_bar_chart.py,sha256=EKc4EWYf7MsZ_tXO5ZAhWfr9ncK2DwUMFC4ZFIjZ9-c,8736
altair/examples/donut_chart.py,sha256=WNWPMwOL1Gtv-x2hhJzezMZvTObgFgmK4JnTZJuOba8,550
altair/examples/dot_dash_plot.py,sha256=mlQsOuWJCtJRPhz57eDkJFqN2hG7bqe1WDCKZJKJIAg,1250
altair/examples/errorbars_with_ci.py,sha256=A2MI3-mRJky9mz_dQO5fqvw7JFGn8ziiSlz9IRhgQtQ,738
altair/examples/errorbars_with_std.py,sha256=ityfdTEUl7mvQjb9vY87DTHwZwFopmDoshaEyoSdPpg,610
altair/examples/falkensee.py,sha256=UzuZaP6XZmzp1uWOAhLmkOD3RtwrA7kkDLrW9rsr3iM,2543
altair/examples/filled_step_chart.py,sha256=ZhnZxUeCY9u8qbZAgKuCP1teHR2R4rq90fX09GAhq3o,441
altair/examples/gantt_chart.py,sha256=hQIjGel87TkrzVnl2K98IBwyC09GquHu_sIu7QifFQE,390
altair/examples/gapminder_bubble_plot.py,sha256=MPkrtwk-Bi4u5-ncLsDcmLQZ-eU0gh7jtJj4x3X9Z3I,637
altair/examples/grouped_bar_chart.py,sha256=A_dg0R7zwoA3ZOVAikP-ZYJ1Q9oMsAjaR5PlzHHJUQY,301
altair/examples/grouped_bar_chart_horizontal.py,sha256=m6VZJtMWB2bpn1SNey8zQs278Leq391n-ANNZ8ZtzFs,331
altair/examples/grouped_bar_chart_with_error_bars.py,sha256=Ae5dhwbo32TUNkOe9HvlwzCGXb5q-PZ_eRU87P0dpng,524
altair/examples/hexbins.py,sha256=LUPzikKQ1oKa2KZUanMzw-YzBfHHPBz_dtFaFbMoWZ8,1504
altair/examples/histogram_responsive.py,sha256=6O76pxAX0dYQQYzgpsHPT_Jg4kO_toompUo_1DGOX7Q,806
altair/examples/histogram_with_a_global_mean_overlay.py,sha256=CeJHYwrOEsGuzagFsu9tpM8_yBTXzgI92FPb5GxJXvM,477
altair/examples/horizon_graph.py,sha256=SxZOZVdBmNlJRv6KW2COyRnho1sVu10mNcUM77JkWz8,1138
altair/examples/horizontal_stacked_bar_chart.py,sha256=SpBPlQpecXiJwe7v8oqWn7VzcXsUxIwHMfrU98oUNXQ,407
altair/examples/image_tooltip.py,sha256=C24QIbZDDiGQQTkXIVF-3Q54VNVDhP2JqBjch81iKE8,588
altair/examples/interactive_brush.py,sha256=ecx2WydJySufORz4YJK8Hlo-e0-ct4qihaEOI7zO16s,548
altair/examples/interactive_cross_highlight.py,sha256=4rdouQ5M0sseDKKxPD8QSxHsCnXpB02v3_k3QrbwhE0,1235
altair/examples/interactive_layered_crossfilter.py,sha256=jW4PFEYwpbg6N1IAxB4-HV2OJNCtCE0KIPYVcLQr6eA,1129
altair/examples/interactive_legend.py,sha256=cRPn1NoiBNmdzAb9mvNq3xs8fBa6riwla82Czt1PTQQ,825
altair/examples/interactive_scatter_plot.py,sha256=RV2Ni8-ZuG464XxdQS-VmpDVcITHfcfivNCAPsbCUhc,373
altair/examples/interval_selection.py,sha256=KlrJJogFI2dOSO0nW4q8ZdKFijY6s3vxKvbgbm-xe94,641
altair/examples/iowa_electricity.py,sha256=LKFZor-7qpNAldhLAdb04B4DcQqIfvxpSQFMMzxguLM,674
altair/examples/isotype.py,sha256=OdOhupSKQxWDH_gIcg9HIE8cZbEwuAD1APb1cIfstkk,6276
altair/examples/isotype_emoji.py,sha256=thDURSqddH3LXrhMtBi7REajPzwMkeUTaSJlMzUtzP8,2922
altair/examples/isotype_grid.py,sha256=skEVMG3dAW8vK77DMq4OL7HGUCz6raHdE2Gs7Rb7YNs,938
altair/examples/layer_line_color_rule.py,sha256=1Ep0pcASLB0DOrmxOq1EXswX9V7fB__5shKojqQNzOs,627
altair/examples/layered_area_chart.py,sha256=3yWjWBMEQAoJ0-eEfrawCXJBiY8K1vh8adqm4RqGP2I,331
altair/examples/layered_bar_chart.py,sha256=ImwTAnX4qR1UlBAWXbuwxnCNdMswG7V-hGDeYekvguE,365
altair/examples/layered_chart_bar_mark.py,sha256=GservBQYjPaQlAkoxbGlSI630msuF6QlKceY6ZEUUDM,631
altair/examples/layered_chart_with_dual_axis.py,sha256=P2_W39fAbY65d52YhsvUOUQ6RdmIWqI2P43hIPxAPrM,787
altair/examples/layered_heatmap_text.py,sha256=HQkIxcEugoJw3Hn13Y0fDnoG8Svv8yyR_teGEuJ7pjk,921
altair/examples/layered_histogram.py,sha256=RGTDOm6uGAwqveT1c3fTpOIuUhjGD846jXCK1CcMH8s,694
altair/examples/line_chart_with_color_datum.py,sha256=g-SdxBYrWdA_XF-zJ8TmcWuiPGn7eUlcGpBevxwKcWE,702
altair/examples/line_chart_with_cumsum.py,sha256=j7ZXWaZydYD36txk_VsxDJSpommLGGx3ZncOy8s39iQ,740
altair/examples/line_chart_with_datum.py,sha256=0nkfipmIi4bhFEruqxEtSudp4ydJMbsOzm_2SKfQZR4,787
altair/examples/line_chart_with_generator.py,sha256=PfyT63ohSfalQ-zbuZyBfd-qKPhtSB6kuvQIO1UvQZo,463
altair/examples/line_chart_with_points.py,sha256=1a3shX5cGix-9QcQHlZI41C-4rdldJACoHJ_i0_x_m8,493
altair/examples/line_percent.py,sha256=wo19DlUtBnBZtIe4z2WmPaOmdb2nWAOKOt5ZFuCppF4,434
altair/examples/line_with_ci.py,sha256=LN_UmvAXmE0BVCg3KJX7vxl0QcRtgmYtFOaDXyox-Eg,502
altair/examples/line_with_log_scale.py,sha256=iIfZUs7fVwHhj5WEEl4rR-gYsiDsV9-1KcuXoIBcVFI,445
altair/examples/london_tube.py,sha256=JRPtjHATDxxk26dFr8PG7S7vsLkXnyjanst0lNDaHTs,1852
altair/examples/multi_series_line.py,sha256=X6Tcz9QwFT8EckQjOOH4zrzBNVcdT7FXPJScuh8n3w0,422
altair/examples/multifeature_scatter_plot.py,sha256=fceg3iD9tnPHHAlLBQLqAI9RjilE0pE3KexiJUNZyZ4,445
altair/examples/multiline_highlight.py,sha256=HVyTtvH7MPm8qZYDDtgrdrWdT86tgfW4litC0pg-9qY,879
altair/examples/multiline_tooltip.py,sha256=2fg4eyHcgJkPiBAF31EkgFYBL3tzs9FNfpbjI1h2Gas,2146
altair/examples/multiple_interactions.py,sha256=SZLzC-N2iMkJLFhgDFKuGhWmdTWrGzt-1blRQApQbQU,3060
altair/examples/multiple_marks.py,sha256=X3RcF-KgnS1rwmvJPb0_jQrKIuGapDaERlzHM4LEH0Y,344
altair/examples/natural_disasters.py,sha256=uO7JCar4YAede9ByNti9KEPF8gP2scil9ITdUiBQiGY,679
altair/examples/normalized_stacked_area_chart.py,sha256=t-vF9aK2S6xy9fIDqOB280icIHN95ALCve0hg1ITkQ8,372
altair/examples/normalized_stacked_bar_chart.py,sha256=lJ0TunrVFP0vf4RL_qTPyVLENPPZeQoaF-hUlAAVpPY,433
altair/examples/normed_parallel_coordinates.py,sha256=yGbJp6av0bW6Ls74xoUVN99S4kWTt2hgZb8sd4sppME,1300
altair/examples/one_dot_per_zipcode.py,sha256=RwSx0zUP2SlUHd6Tp05TisKYuH1OJWfouDP7W53oxTU,614
altair/examples/pacman_chart.py,sha256=71BEC1BurupYSElW9_cJm1i3nSZNTcxPTdNM98QCGHM,462
altair/examples/parallel_coordinates.py,sha256=2DBsBMjY-xPWCNVhLHvdpeb52eQdBbb1YadmNkKiYuw,818
altair/examples/percentage_of_total.py,sha256=qsI9eDq859Fvsgh2hMp1HQt041LxTwXkwG0AJwcIaO0,631
altair/examples/pie_chart.py,sha256=XVl1Xv28tWb6fCj5ootGPSLUd8nVipLFnla_4svDOU0,526
altair/examples/pie_chart_with_labels.py,sha256=yHHz8ikObuvLzBpFqmXttuTsMRjJLiF-sm9znlIyEp8,703
altair/examples/poly_fit_regression.py,sha256=gjGiBJhCkLcsd4L7mwVnTY9UlfVK0BcVDIacdYMTi9c,913
altair/examples/pyramid.py,sha256=DLWFwSMNLLH9iGuPPyYvuEzzMbav5bX-1pkcsCQ_lGQ,701
altair/examples/radial_chart.py,sha256=HMEByn8iddGwkK3eoaIBx48DlVoO9sMLeLkl8LtLISM,699
altair/examples/ranged_dot_plot.py,sha256=tgNhVtH5ANA-MlToN7J1mxjvd0wgUI0onioCsye0yuY,1029
altair/examples/ridgeline_plot.py,sha256=wuZfkYilKlEYkShz15JviQiE3DFyK9yBIHkHAhRb_-o,1609
altair/examples/scatter_href.py,sha256=27uJ8p3LRmNpV7cDEk_vRDchX844MxtNtGV8G070yCo,590
altair/examples/scatter_linked_brush.py,sha256=gyLjkTYKR95YriavW-2LIlYD2IgUkcTJssy7yHcXyeM,641
altair/examples/scatter_linked_table.py,sha256=NiHRrFXTjAhB_TofZEGoWddJIJY4HZcDCEBfYoIofwc,1363
altair/examples/scatter_marginal_hist.py,sha256=-cMYqSIvOIN5G9pC-2-iI_N5eacDxwLIp8hEkgOA6jg,1401
altair/examples/scatter_matrix.py,sha256=diuIoFGoz_6Nnzh8AOYDEqStiKVkPefZYReqdgDrydE,604
altair/examples/scatter_qq.py,sha256=1VxvVjhbLwew4HRQz4GfDsjb5fzSoBlJejb5W0UCtSY,531
altair/examples/scatter_tooltips.py,sha256=c61FoskgICS-CtBxHnJvjr9b8rBRTWBrIn5w9_XavBs,548
altair/examples/scatter_with_histogram.py,sha256=KwIZJdzPT2n-pYg4xk5Wg6xZGYT8W66mGvt6Fxb5FMQ,1465
altair/examples/scatter_with_labels.py,sha256=Xeb5qAdR2R7q8jGlwkJcAwmvcUaThqdtlIFnJHr4onY,527
altair/examples/scatter_with_layered_histogram.py,sha256=s1b1NMZ8RiI_Io-Yo3eah_bzNL81ps0tS0u-UjvUDow,1966
altair/examples/scatter_with_loess.py,sha256=YIHt0amW7yqSgcz3vQcx1LP7p4ZJKzFBYFgrKzoiu00,755
altair/examples/scatter_with_minimap.py,sha256=8VHs89wo8ES1udPHotLyZdS4WOz8qvhDuxAmdbMvdVU,1189
altair/examples/scatter_with_rolling_mean.py,sha256=P3jehhreowoI4Bb7fs3jwlGYThZKTA2dqGEOuuA2vNM,680
altair/examples/seattle_weather_interactive.py,sha256=YnWHGuJizs_Yyal3g9XjmTq9NY2ccF2rtGfQl_BQ194,1662
altair/examples/select_detail.py,sha256=1rfKfQY4YBz6lJKvyeW5poEZZUsAABJjqwVuwVQ4Oys,1944
altair/examples/select_mark_area.py,sha256=h9B6RcRFeAiZkx6iiAxR-W8S7XXed4J_eg9psLooo_Y,831
altair/examples/selection_histogram.py,sha256=YZLwLRml-LDlpWA6Lp1UfZdKNsV27ErgNpUiom_P3bk,740
altair/examples/selection_layer_bar_month.py,sha256=AjmJqMGnrmT5XqlfYjFrS1SEBE3UiLkbd6JKKp6Pnpc,835
altair/examples/simple_bar_chart.py,sha256=eQDphzlvr4bAQkzZMFdmsmX_QkXTqpPrhALVqhEypnQ,358
altair/examples/simple_heatmap.py,sha256=KiLVSklGeeKSGUVtwC-WvkI-Ele_xKIns4mPfOVWFOU,545
altair/examples/simple_histogram.py,sha256=tV-FNXkW6iWhGU3-Shc9mny1H-fTk-W39oRgNFmK0YE,376
altair/examples/simple_line_chart.py,sha256=O8tGDmpuN55Dv-OOKR2C5lJn5K-9H3Cd4R5HmkyZVd4,361
altair/examples/simple_scatter_with_errorbars.py,sha256=SQYrejTr6SnP3GdoC1ZCVZd4YLh1WCkG_fk5exrsET8,896
altair/examples/simple_stacked_area_chart.py,sha256=Jf4RWriWjF3E8Ee2gUllOTNyO_-73OfFuduCMYpTaBY,336
altair/examples/slope_graph.py,sha256=Bmmj0-qSVxRiwyVN_VosVtZ_xrY-KBao_B--D-st55I,285
altair/examples/sorted_error_bars_with_ci.py,sha256=_o0MkLyxs3aZyqexQdsNIuz9iwtWXJdtNy29r_oQfF0,783
altair/examples/stacked_bar_chart.py,sha256=i4fvIg8B1KVMFMy-eZIGLyrT1uOpQ4o4Jc7uyitXN_Q,375
altair/examples/stacked_bar_chart_sorted_segments.py,sha256=tZWN7rYRmzPClW9YJ9PqevlwsuVSlf5rGQU09PZYT90,478
altair/examples/stacked_bar_chart_with_text.py,sha256=qtgKK56f5d2QbtN3edagbmK1glpUFKizv-7yiXycTz0,743
altair/examples/stem_and_leaf.py,sha256=rtUIX90w_t-Y4mqLXsVfYlwRHC0-V0y1tx5OSsDInSM,949
altair/examples/step_chart.py,sha256=cPpYYlKPRDlOrbv6A46deAgfpYPkJB2QWxdA1iwZpVA,599
altair/examples/streamgraph.py,sha256=Wd8tUo2sVg0BOMbTqL7llG_tcPk333K0DhVBhjXOA48,510
altair/examples/strip_plot.py,sha256=raHaCmiLmrmp_QE-EzMZSvfnTa9mgRIXN0h33vaBnL4,274
altair/examples/stripplot.py,sha256=snLfl7yf0fVkmoLslMeFI_uWcqhEkcYY2xkCgJnIwJw,950
altair/examples/table_bubble_plot_github.py,sha256=q92jIY3LQL6VSNqeKn5DCSSCDw5kwL0JtInJBTW5gxU,379
altair/examples/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
altair/examples/tests/__pycache__/__init__.cpython-312.pyc,,
altair/examples/tests/__pycache__/test_examples.cpython-312.pyc,,
altair/examples/tests/test_examples.py,sha256=kqI5ZMcIYJxZQacbuRXpLr0w10-_-5Hpnx0Lr7ABFgU,1325
altair/examples/top_k_items.py,sha256=6bTY-PaDuu7dmXKJ6lJK9S-UCRo1lvOb0kaCW0NVfqA,668
altair/examples/top_k_letters.py,sha256=gPMDPVQOUcWXJtKXH1CctxCaKh1GyIHILVt02IHjge8,1434
altair/examples/top_k_with_others.py,sha256=hZiV9vLIJY0-IUzIhOqoIAjTqgzyaa2IAxXLNYmGriM,968
altair/examples/trail_marker.py,sha256=zbduBd0ZXoiojSHf_dpYyJtHsqKGyEOQqMlXls7Tqvo,333
altair/examples/trellis_area.py,sha256=kIytcW521C5QIrcCoxQwCOzWpBWKJ0SYpW9OzPzUIUE,362
altair/examples/trellis_area_sort_array.py,sha256=_6mdPYYEymYCObgCP6k_igSWVzTduni0sRXbSxK3XGQ,536
altair/examples/trellis_histogram.py,sha256=OTcTD1LxwItVHaZwHYlEdBAnkW4r4dcVT82h5XGwai0,379
altair/examples/trellis_scatter_plot.py,sha256=sEQcwth9PDGdvGkR3f8OfSh44cGtX0ERaZKyxLltpfE,319
altair/examples/trellis_stacked_bar_chart.py,sha256=jhgRMJLm2yilmaezlvqZl7lMjC1v7n2bDvCFEqFDSPs,437
altair/examples/us_employment.py,sha256=VfmN6gAPtgXv_uTBsUHOLRfL2JKbGlFXyBeEm0u3qdE,1400
altair/examples/us_incomebrackets_by_state_facet.py,sha256=U87TPjymzdWVCULN1jnk058fP_gPTSpRCKpbt14z7yg,659
altair/examples/us_population_over_time.py,sha256=J9N9M5Z7yjhVVC8WqmSuIDJn_sbr3sdxy1PbjOSgO90,1045
altair/examples/us_population_over_time_facet.py,sha256=dHgot_A34DW-fnzF--OnpwMO3cYtzXwERqA1O86Cq5o,584
altair/examples/us_population_pyramid_over_time.py,sha256=r9FHIFrUlpgblyUgtgktR-FPDjDpf373ZrHLua6ymvA,1598
altair/examples/us_state_capitals.py,sha256=8QhflxEhCnig2CR7p3gcameymz1-LBtm6GDN71JeL2Y,1121
altair/examples/violin_plot.py,sha256=jzc1MtfuhAKNmzwvkAsgZGzVp84MtGQ7Ft4caRGjXW4,888
altair/examples/weather_heatmap.py,sha256=anGAKh4cGKjYYRdCK_3Z0DxI8Cv51W8-5_vH-lbPgQM,679
altair/examples/wheat_wages.py,sha256=qFdT8Tknu8sPY0vdPh-4TV4ArqX8EkblivTZTvwhWHQ,1850
altair/examples/wilkinson-dot-plot.py,sha256=6rFzQHL4q8PiomMhail_1jh4fs3VRqZUxqr9LXeybEI,561
altair/examples/wind_vector_map.py,sha256=I219zCzm_jgZIrHxXz5dR_kmuNwfKsmbqQO5pBcuo_I,849
altair/examples/window_rank.py,sha256=gfRD8UdHtPfUuIWFt6_pDqkfDGDj_9P7E0Bu09SGOnE,1658
altair/examples/world_map.py,sha256=NPWlbLMrKvUKs6RKI4Wzgu2AP_i-VMd9lU2r-pPDoV0,722
altair/examples/world_projections.py,sha256=w4gK-2y2qMQK5IauwYc8cN4JzL-1OkxeV72bnUyIiGQ,709
altair/expr/__init__.py,sha256=kt48-Jux39zZUt-rpOlGgOfWdsE3IrecrHCc8Vsh5dI,171
altair/expr/__pycache__/__init__.cpython-312.pyc,,
altair/expr/__pycache__/consts.cpython-312.pyc,,
altair/expr/__pycache__/core.cpython-312.pyc,,
altair/expr/__pycache__/funcs.cpython-312.pyc,,
altair/expr/consts.py,sha256=VVIfpd7tbwmEky8wjMZqojh6tc0ei8UMdZKK46njZC0,875
altair/expr/core.py,sha256=1Bh8SJFVbkBD4EYJHAWJsh6Oe74_en_0ZfUMX9L5iwk,5313
altair/expr/funcs.py,sha256=yRyirJU7aHtOMdmFUNIgnYPDrlPX02_5-Cw25DUMQ6s,27440
altair/expr/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
altair/expr/tests/__pycache__/__init__.cpython-312.pyc,,
altair/expr/tests/__pycache__/test_expr.cpython-312.pyc,,
altair/expr/tests/test_expr.py,sha256=MJER3k7ebewF9GhwtjDW7UJbkp-gf9cPRlS_jCWoEys,2880
altair/sphinxext/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
altair/sphinxext/__pycache__/__init__.cpython-312.pyc,,
altair/sphinxext/__pycache__/altairgallery.cpython-312.pyc,,
altair/sphinxext/__pycache__/altairplot.cpython-312.pyc,,
altair/sphinxext/__pycache__/schematable.cpython-312.pyc,,
altair/sphinxext/__pycache__/utils.cpython-312.pyc,,
altair/sphinxext/altairgallery.py,sha256=cuKmz6raq5NUXNUBeT8nW2iW-PkvQUsWXcb5s8e8PzU,9291
altair/sphinxext/altairplot.py,sha256=Y0baQCO7woJ_HNZoRgOZwUHj-IWmqA8KP3ybAAwuxOk,11077
altair/sphinxext/schematable.py,sha256=HkiHbTMmKyt2A36Dx22pWMbv1hi9tVXardD2D04q3Mo,5433
altair/sphinxext/utils.py,sha256=vrV5A88pXMdkWfsmaPBGfzqQFO1k8o3M-GC7O0YzjEE,5859
altair/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
altair/tests/__pycache__/__init__.cpython-312.pyc,,
altair/tests/__pycache__/test_magics.cpython-312.pyc,,
altair/tests/test_magics.py,sha256=8lc2MMYBMn61EizOekey7ShmZkVkr1V8ysUEHfOvSOM,5578
altair/utils/__init__.py,sha256=lzxxS5tSM4q2aoYSBjiN8lAsbHV8upiNvoz8CuUe4As,677
altair/utils/__pycache__/__init__.cpython-312.pyc,,
altair/utils/__pycache__/core.cpython-312.pyc,,
altair/utils/__pycache__/data.cpython-312.pyc,,
altair/utils/__pycache__/deprecation.cpython-312.pyc,,
altair/utils/__pycache__/display.cpython-312.pyc,,
altair/utils/__pycache__/execeval.cpython-312.pyc,,
altair/utils/__pycache__/html.cpython-312.pyc,,
altair/utils/__pycache__/mimebundle.cpython-312.pyc,,
altair/utils/__pycache__/plugin_registry.cpython-312.pyc,,
altair/utils/__pycache__/save.cpython-312.pyc,,
altair/utils/__pycache__/schemapi.cpython-312.pyc,,
altair/utils/__pycache__/server.cpython-312.pyc,,
altair/utils/__pycache__/theme.cpython-312.pyc,,
altair/utils/core.py,sha256=DwG3o6-tqc_dxRxphScngTCA15vNjXx7XV90JugolVo,21953
altair/utils/data.py,sha256=SIaxDkFowJ1jYmvLc6jKeS3bPcGR-slrQ2nOg3inz-I,8047
altair/utils/deprecation.py,sha256=8X8JTIubRAeYCU7FdCh_62hiZsjItKe2goi2KDlU3PM,1744
altair/utils/display.py,sha256=xp3czad3sncKy56lV1tM924qgElFSEaTETLddPeWYkM,6289
altair/utils/execeval.py,sha256=8dcrGym8BA2OEXeA-bzF-eeEDYaMgYJZUfE5a2cqixU,1598
altair/utils/html.py,sha256=IjSMS1O4OtlF_1VTFbZjRT3hd6gajGMwLtAaa6pTG7c,7908
altair/utils/mimebundle.py,sha256=YCjaN0mF3Z4MxKzqfibjl8i3fxAGe6nefsGV3LdbVbA,2857
altair/utils/plugin_registry.py,sha256=s6l99oh8gEEmNr-WL8L6p-9KJhSK1RCq7czmYrKJoww,7000
altair/utils/save.py,sha256=zhvOBzj8yyb5jpPWyxA5dLcOtsiEnbgHldOKIoBIfpE,4455
altair/utils/schemapi.py,sha256=DxjrrsOb_zuv0LoXvS96n_MZobsU0daEv4Yn478lxKY,21268
altair/utils/server.py,sha256=0H0ZfGOSoHwwm6Tg8SYSk6lr4YPAB5vvMBt4kkPEs78,4091
altair/utils/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
altair/utils/tests/__pycache__/__init__.cpython-312.pyc,,
altair/utils/tests/__pycache__/test_core.cpython-312.pyc,,
altair/utils/tests/__pycache__/test_data.cpython-312.pyc,,
altair/utils/tests/__pycache__/test_deprecation.cpython-312.pyc,,
altair/utils/tests/__pycache__/test_execeval.cpython-312.pyc,,
altair/utils/tests/__pycache__/test_html.cpython-312.pyc,,
altair/utils/tests/__pycache__/test_mimebundle.cpython-312.pyc,,
altair/utils/tests/__pycache__/test_plugin_registry.cpython-312.pyc,,
altair/utils/tests/__pycache__/test_schemapi.cpython-312.pyc,,
altair/utils/tests/__pycache__/test_server.cpython-312.pyc,,
altair/utils/tests/__pycache__/test_utils.cpython-312.pyc,,
altair/utils/tests/test_core.py,sha256=mRK3DD2A_YHBU3DfiPEbYliAWyvc88Ie7qcg8sYVEoI,8233
altair/utils/tests/test_data.py,sha256=Cc_SzNP-o-dq79Jy_RZRn4BMTO77wRIa_1KXT25Kux4,3861
altair/utils/tests/test_deprecation.py,sha256=DJtaSu6iSUW-dKvsgFEkKXK6_YfA1PK2_9fgr0JJn1I,690
altair/utils/tests/test_execeval.py,sha256=F4Lcq49_0DHWRBapHh2cVh9MfMhbvETTDngzZ5mDHfw,530
altair/utils/tests/test_html.py,sha256=cd-7TQUoYgtmT7acVywAGKPl_3QC4tmyYqQ7_QndRd8,1439
altair/utils/tests/test_mimebundle.py,sha256=yaxlAqxDkU9X_MDA6v0cjAR8h-Xg6NitXGA8ZUFqBdM,6400
altair/utils/tests/test_plugin_registry.py,sha256=2S6KUrzULrXpAYcqon3Tw9Ih4XXgPaMhLMx9jSB-w0k,3613
altair/utils/tests/test_schemapi.py,sha256=_ulbI1ztx0dYJ1gGzQPyp0P3QkiL8eR2oE4r8vo_Eis,9385
altair/utils/tests/test_server.py,sha256=btAdR-s-NGo2XHKMINrfEvZIs2Gz0U3BhEced3MeOE4,230
altair/utils/tests/test_utils.py,sha256=DgIcDFxyknhY-RgPuwYgUzjQKb5ymDckX0-56OWCrgs,6528
altair/utils/theme.py,sha256=b1VS--VOilgE8hrw05-DjIz_d5ND8qVqTrT-OWTqD_Y,221
altair/vega/__init__.py,sha256=EGYjKbYbl2ZRaw1vUIw7LhqbomxaYV8kLb71r_2UWYM,33
altair/vega/__pycache__/__init__.cpython-312.pyc,,
altair/vega/__pycache__/data.cpython-312.pyc,,
altair/vega/__pycache__/display.cpython-312.pyc,,
altair/vega/data.py,sha256=BXgUhbfanZLhUgLjGz2ucwPHjhN7LVUGjDKqe9gNJE0,1024
altair/vega/display.py,sha256=z2LFWYDNtk9OPq9_luZMqS6oqVxSxV9RwddQQK4ZEtE,306
altair/vega/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
altair/vega/tests/__pycache__/__init__.cpython-312.pyc,,
altair/vega/tests/__pycache__/test_import.cpython-312.pyc,,
altair/vega/tests/test_import.py,sha256=1EC96TP388IZMuHyRJ0NBPGd1HUqBBijsHw4JPtbPOM,120
altair/vega/v5/__init__.py,sha256=60gWFel_o0oCAaoHAxJP0yoKQ4jrtoMCbU61UXL3ZC4,424
altair/vega/v5/__pycache__/__init__.cpython-312.pyc,,
altair/vega/v5/__pycache__/data.cpython-312.pyc,,
altair/vega/v5/__pycache__/display.cpython-312.pyc,,
altair/vega/v5/data.py,sha256=UtCpYu_MdLHYCjLJgj9fUqqrHbdi6OuvUOWwLA7DjGw,522
altair/vega/v5/display.py,sha256=yf1nGxZMow4y9I0cb2eCOFhe3mS17jnFLTVExxRociY,3104
altair/vega/v5/schema/__init__.py,sha256=yOWCNUMnPuWF3LXP5joooTxiGk4iBnPN8Bm-BjO_hmk,126
altair/vega/v5/schema/__pycache__/__init__.cpython-312.pyc,,
altair/vega/v5/schema/__pycache__/core.cpython-312.pyc,,
altair/vega/v5/schema/core.py,sha256=bE6_b6O50EVayrtYc4Qx0DEZtxZSb3pe9jiRJUao3rs,111093
altair/vega/v5/schema/vega-schema.json,sha256=Tq6GmZs0fUuetFMQkibodoja9KfOuhHxTBdhC7Dz2W4,399098
altair/vegalite/__init__.py,sha256=Z4Kae_gmqsdjLhutwrKTLHw-2j3_OPEAx0SVuVcyvFw,33
altair/vegalite/__pycache__/__init__.cpython-312.pyc,,
altair/vegalite/__pycache__/api.cpython-312.pyc,,
altair/vegalite/__pycache__/data.cpython-312.pyc,,
altair/vegalite/__pycache__/display.cpython-312.pyc,,
altair/vegalite/__pycache__/schema.cpython-312.pyc,,
altair/vegalite/api.py,sha256=1jaXpR7lyPrmBEihR40BtuytE2ozdLOxCqz3nExQTjo,37
altair/vegalite/data.py,sha256=6D0PSRFDDNhLEkM9AXx7yQEloR43u8G6FmjvbSdS7z0,1008
altair/vegalite/display.py,sha256=i5WPQsUD4x7vfI6yaOe1DX6vRr7ftcHBgYgXR9R0LWQ,276
altair/vegalite/schema.py,sha256=aQpkwVeKCpZGhrFAPUI1SXZ3t5xotojaTONEU9Y_9xc,69
altair/vegalite/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
altair/vegalite/tests/__pycache__/__init__.cpython-312.pyc,,
altair/vegalite/tests/__pycache__/test_common.cpython-312.pyc,,
altair/vegalite/tests/test_common.py,sha256=G2_NVFkpwlt1S_vRACCIYY8EAcqcA6-OP7B1o05GKmQ,2602
altair/vegalite/v3/__init__.py,sha256=18soa2yRr3vbp5YadmaYYUxaFVt-1Xr72IRcLENQeDk,697
altair/vegalite/v3/__pycache__/__init__.cpython-312.pyc,,
altair/vegalite/v3/__pycache__/_deprecated.cpython-312.pyc,,
altair/vegalite/v3/__pycache__/api.cpython-312.pyc,,
altair/vegalite/v3/__pycache__/data.cpython-312.pyc,,
altair/vegalite/v3/__pycache__/display.cpython-312.pyc,,
altair/vegalite/v3/__pycache__/theme.cpython-312.pyc,,
altair/vegalite/v3/_deprecated.py,sha256=JJmcGZAAy499ZDIwfQe2zoTCuzFu0r6aWxn0VMEhwx4,1058
altair/vegalite/v3/api.py,sha256=5pZNIyI-fOwK0r4BSBDJk_Z8mHzPNoiG1yGIjlKGaOI,76424
altair/vegalite/v3/data.py,sha256=Xh1iAwQynQEU0aGhISWKG4JpjsvWhvgjAx3Jxic9zSg,961
altair/vegalite/v3/display.py,sha256=W_gxJcVHhwoDa1d0kyMTAf03rAcVe5HFUC8yiVSReYI,4362
altair/vegalite/v3/schema/__init__.py,sha256=WwFnvbwSfSXjQ4NSg5VsCS_ii8RBtg-kZVbEuz4rDJQ,152
altair/vegalite/v3/schema/__pycache__/__init__.cpython-312.pyc,,
altair/vegalite/v3/schema/__pycache__/channels.cpython-312.pyc,,
altair/vegalite/v3/schema/__pycache__/core.cpython-312.pyc,,
altair/vegalite/v3/schema/__pycache__/mixins.cpython-312.pyc,,
altair/vegalite/v3/schema/channels.py,sha256=G35HMHAUByV_iBhX5TnPjSSlsahBkuBSGkDH-Et2WU8,263403
altair/vegalite/v3/schema/core.py,sha256=1AdpreX9RH0cPB952PI8fzmKwDsWoPG6lWMxxz7fxhw,751732
altair/vegalite/v3/schema/mixins.py,sha256=-rmQuE5Ow3jdPIuLnLjPmkqR71teUnhUBSOqpaiMnxQ,47162
altair/vegalite/v3/schema/vega-lite-schema.json,sha256=a0r_kdFQ-8L2BJj2bEaNvFIDQ9kDQm4882oeK3_yfEQ,678330
altair/vegalite/v3/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
altair/vegalite/v3/tests/__pycache__/__init__.cpython-312.pyc,,
altair/vegalite/v3/tests/__pycache__/test_api.cpython-312.pyc,,
altair/vegalite/v3/tests/__pycache__/test_data.cpython-312.pyc,,
altair/vegalite/v3/tests/__pycache__/test_display.cpython-312.pyc,,
altair/vegalite/v3/tests/__pycache__/test_geo_interface.cpython-312.pyc,,
altair/vegalite/v3/tests/__pycache__/test_renderers.cpython-312.pyc,,
altair/vegalite/v3/tests/__pycache__/test_theme.cpython-312.pyc,,
altair/vegalite/v3/tests/test_api.py,sha256=evdq8qG-BO3g7cI8wc-5mTj_YQmpZXo4YgzItArz0fQ,29937
altair/vegalite/v3/tests/test_data.py,sha256=cKfkUP015m6pYsYq9kf5fTkjwy7oY1TmWzlRGsc7PAo,966
altair/vegalite/v3/tests/test_display.py,sha256=SiDZmqiNjva-Z-VRPxncC_T9WOTbNRAww9Q4bN5MWJM,1976
altair/vegalite/v3/tests/test_geo_interface.py,sha256=VehjMM4o4MvmP-xMTX_uzXrLVha6sZ23S6aaDW0GTPE,6496
altair/vegalite/v3/tests/test_renderers.py,sha256=RTEGP_aerLC4R2Rkow9SJqgFltqhzItdecRwDSgULcM,2993
altair/vegalite/v3/tests/test_theme.py,sha256=bJfvbZAqrShvtSc6gyqRxvmnocx572MWr6Mdfa7q1rM,525
altair/vegalite/v3/theme.py,sha256=meH6Qy5EOXP7kLS_TUBTjbg18h4deVe6pPMFifkrphc,1435
altair/vegalite/v4/__init__.py,sha256=ncmtsjAQ_9U7ZXJNEMqWxJ8pCl_G5MayleE_74O3og0,382
altair/vegalite/v4/__pycache__/__init__.cpython-312.pyc,,
altair/vegalite/v4/__pycache__/api.cpython-312.pyc,,
altair/vegalite/v4/__pycache__/data.cpython-312.pyc,,
altair/vegalite/v4/__pycache__/display.cpython-312.pyc,,
altair/vegalite/v4/__pycache__/theme.cpython-312.pyc,,
altair/vegalite/v4/api.py,sha256=lF9oI8B7f_xLxjdETnC1ei0zal1YBcmASOfBH-7cQ7E,89700
altair/vegalite/v4/data.py,sha256=puAJ0gsSRS4d7FBI8H3el9spu_UIFykLPrU8HldT6-c,961
altair/vegalite/v4/display.py,sha256=GcsGE0qehKAdvdPMHrkPJuw40ujcshDjEgfjolHxSYU,3522
altair/vegalite/v4/schema/__init__.py,sha256=hnPGkzXrf8kzWWX8SGCUSGEJbtFNKVhV4_jbBxzN4a4,154
altair/vegalite/v4/schema/__pycache__/__init__.cpython-312.pyc,,
altair/vegalite/v4/schema/__pycache__/channels.cpython-312.pyc,,
altair/vegalite/v4/schema/__pycache__/core.cpython-312.pyc,,
altair/vegalite/v4/schema/__pycache__/mixins.cpython-312.pyc,,
altair/vegalite/v4/schema/channels.py,sha256=WrSUJ7rPI05vxyuLAghSV2W1NtNJnodghVihrK_9s0w,583838
altair/vegalite/v4/schema/core.py,sha256=Wm4EYUPy41RzKEtYXqeetHyvT8D1E2HBiAoP8ffwZbc,973217
altair/vegalite/v4/schema/mixins.py,sha256=R2IItJP-yiP__nX2PFiXPoQ8nSo4RB0bLHgUn7Q-F80,85182
altair/vegalite/v4/schema/vega-lite-schema.json,sha256=IfCmoCg4dEdFQdbx-IcShsfmvybY7i6phMc_MdzRwj4,1813485
altair/vegalite/v4/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
altair/vegalite/v4/tests/__pycache__/__init__.cpython-312.pyc,,
altair/vegalite/v4/tests/__pycache__/test_api.cpython-312.pyc,,
altair/vegalite/v4/tests/__pycache__/test_data.cpython-312.pyc,,
altair/vegalite/v4/tests/__pycache__/test_display.cpython-312.pyc,,
altair/vegalite/v4/tests/__pycache__/test_geo_interface.cpython-312.pyc,,
altair/vegalite/v4/tests/__pycache__/test_renderers.cpython-312.pyc,,
altair/vegalite/v4/tests/__pycache__/test_theme.cpython-312.pyc,,
altair/vegalite/v4/tests/test_api.py,sha256=xCt3YJCQFYxL1rk-hoj3UtGMQNhFbTJfzgDr39VuKF8,30953
altair/vegalite/v4/tests/test_data.py,sha256=cKfkUP015m6pYsYq9kf5fTkjwy7oY1TmWzlRGsc7PAo,966
altair/vegalite/v4/tests/test_display.py,sha256=l382FqqwaeJ1mgIx2aBYL7xSrJcv_gdtCH_K1EJ7GvY,1976
altair/vegalite/v4/tests/test_geo_interface.py,sha256=9K4YqtljdMo9ZqG_XEyyHMmN7eLQYiQ_wvWTJB1mlug,6496
altair/vegalite/v4/tests/test_renderers.py,sha256=OQkoRdCpMLxiHbMsC4c_cVS5caMr6ZJPbZ9_4hCB7zA,2477
altair/vegalite/v4/tests/test_theme.py,sha256=khpwI-KNQvPicOru4mGU8Kl9lxPqaoEh2ePC8_AiERA,505
altair/vegalite/v4/theme.py,sha256=jd1OmLc0ic2jPkgdgk4DqF-hBCDOqyw7KyZbuACDRRs,1390
