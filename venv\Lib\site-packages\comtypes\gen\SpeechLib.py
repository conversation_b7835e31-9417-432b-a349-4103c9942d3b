from enum import IntFlag

import comtypes.gen._C866CA3A_32F7_11D2_9602_00C04F8EE628_0_5_4 as __wrapper_module__
from comtypes.gen._C866CA3A_32F7_11D2_9602_00C04F8EE628_0_5_4 import (
    DISPID_SRGState, SPEI_WORD_BOUNDARY, SDA_No_Trailing_Space,
    DISPID_SVResume, SAFTGSM610_22kHzMono,
    DISPID_SVSLastStreamNumberQueued, SPDKL_CurrentConfig,
    DISPID_SPEDisplayText, DISPID_SVEStreamStart, ISpeechGrammarRules,
    DISPID_SLPSymbolic, SPEI_MIN_SR, SPSHT_Unknown, SPSHT_OTHER,
    SpeechCategoryAudioIn, SDTReplacement,
    DISPID_SOTMatchesAttributes, DISPID_SMSADeviceId,
    eLEXTYPE_PRIVATE3, eLEXTYPE_PRIVATE14, SpeechAudioFormatGUIDWave,
    DISPID_SDKDeleteValue, eWORDTYPE_DELETED, DISPID_SGRSTRule,
    SpeechCategoryAudioOut, SPPS_RESERVED3, SPAR_Low, SWTDeleted,
    SP_VISEME_20, Speech_StreamPos_RealTime, DISPID_SGRSTPropertyId,
    SpWaveFormatEx, SPDKL_CurrentUser, SREPrivate, SPRS_ACTIVE,
    SLODynamic, SpeechAudioFormatGUIDText, SVP_16,
    DISPID_SLGetGenerationChange, SPVOICESTATUS, SPEVENTSOURCEINFO,
    SVSFParseSapi, DISPID_SRGDictationSetState,
    DISPID_SOTCEnumerateTokens, DISPID_SVSpeakCompleteEvent,
    SPEI_RECO_OTHER_CONTEXT, eLEXTYPE_RESERVED6, SAFT12kHz8BitMono,
    DISPID_SRRTOffsetFromStart, SRATopLevel,
    SWPUnknownWordPronounceable, DISPID_SOTRemoveStorageFileName,
    SRERecoOtherContext, SPTEXTSELECTIONINFO, SPAR_Medium,
    DISPID_SVSInputWordPosition, DISPID_SREmulateRecognition,
    SPWORDPRONUNCIATION, DISPID_SGRSTPropertyValue, SP_VISEME_1,
    SAFTADPCM_44kHzMono, DISPID_SVVoice,
    __MIDL___MIDL_itf_sapi_0000_0020_0001, SpLexicon,
    SPEI_TTS_PRIVATE, DISPID_SASCurrentDevicePosition,
    SAFT48kHz8BitStereo, DISPID_SRGRules, SRCS_Enabled,
    SPAUDIOBUFFERINFO, DISPID_SPPsCount, SpCustomStream,
    SPEI_VOICE_CHANGE, SPWP_KNOWN_WORD_PRONOUNCEABLE, SPEI_REQUEST_UI,
    SAFT44kHz8BitStereo, DISPID_SRRAlternates, DISPID_SRState,
    SPEI_SR_BOOKMARK, GUID, eLEXTYPE_VENDORLEXICON,
    SREPropertyStringChange, SPINTERFERENCE_LATENCY_WARNING,
    SPPS_RESERVED4, SPPS_Modifier, DISPID_SRCESoundEnd,
    SPEI_START_INPUT_STREAM, SPXRO_SML, SPEI_TTS_BOOKMARK,
    DISPID_SGRInitialState, SPWORDPRONUNCIATIONLIST,
    DISPID_SPIAudioSizeTime, SPPS_Noun, SpeechCategoryPhoneConverters,
    SSFMOpenForRead, DISPID_SAVolume, DISPID_SVGetProfiles,
    DISPID_SVSpeakStream, SBONone, SP_VISEME_18,
    ISpeechResourceLoader, DISPID_SASState, Speech_StreamPos_Asap,
    SpeechTokenIdUserLexicon, SPPS_Interjection, SGRSTTWildcard,
    DISPID_SPAs_NewEnum, DISPID_SOTCId, SVEViseme, eLEXTYPE_PRIVATE1,
    DISPID_SMSALineId, SPINTERFERENCE_NONE, SVEStartInputStream,
    DISPIDSPTSI_SelectionOffset, DISPID_SRCERequestUI, SPPS_LMA,
    SpCompressedLexicon, SAFT12kHz8BitStereo, eLEXTYPE_APP,
    DISPID_SRGetFormat, SAFTADPCM_8kHzMono, SSFMCreate,
    SPPHRASEELEMENT, SITooLoud, SPPS_NotOverriden,
    ISpeechWaveFormatEx, ISpeechLexiconPronunciations, SPPHRASERULE,
    SPBO_TIME_UNITS, ISpeechLexiconWords, SpResourceManager, BSTR,
    SPWORD, SVEBookmark, SVSFUnusedFlags, DISPID_SBSWrite,
    ISequentialStream, DISPID_SRCEventInterests, DISPID_SCSBaseStream,
    DISPID_SRCVoice, eWORDTYPE_ADDED, SPEI_SR_PRIVATE,
    ISpeechPhraseReplacement, DISPID_SRCEAudioLevel, SPEVENT,
    SpeechTokenKeyAttributes, SP_VISEME_6, SAFTCCITT_ALaw_8kHzMono,
    SDTDisplayText, SRTEmulated, DISPID_SRCEInterference,
    ISpeechObjectTokens, ISpPhoneConverter, DISPID_SRCEStartStream,
    DISPID_SVWaitUntilDone, SPPROPERTYINFO, SREInterference,
    DISPID_SGRSTsCount, DISPID_SABufferInfo,
    ISpeechRecoResultDispatch, DISPID_SVGetAudioInputs,
    DISPID_SOTDisplayUI, SPSMF_SRGS_SEMANTICINTERPRETATION_W3C,
    SPINTERFERENCE_TOOQUIET, SRADefaultToActive, DISPID_SVPriority,
    SAFTADPCM_44kHzStereo, SP_VISEME_14, SVF_None,
    DISPID_SPRules_NewEnum, DISPID_SPPBRestorePhraseFromMemory,
    eLEXTYPE_USER, SPSMF_UPS, SPWF_INPUT, DISPID_SLWs_NewEnum,
    SpeechVoiceSkipTypeSentence, ISpeechPhraseAlternates,
    DISPID_SPPValue, SAFT32kHz16BitStereo, SREPropertyNumChange,
    DISPID_SVIsUISupported, DISPID_SPIAudioSizeBytes,
    DISPID_SRCERecognizerStateChange, SINoSignal, SREFalseRecognition,
    SPEI_MAX_TTS, DISPID_SPRFirstElement, SP_VISEME_9, ISpRecoContext,
    DISPID_SPPId, DISPID_SVVolume,
    DISPID_SRAllowVoiceFormatMatchingOnNextSet, SAFTText,
    SpInprocRecognizer, DISPID_SRGCmdLoadFromMemory,
    SPRST_ACTIVE_ALWAYS, SpeechPropertyNormalConfidenceThreshold,
    SVSFPurgeBeforeSpeak, SAFT24kHz8BitMono, SGPronounciation,
    DISPIDSPTSI_ActiveLength, DISPID_SLGetPronunciations,
    DISPID_SRCERecognitionForOtherContext, ISpeechVoice, SPAS_CLOSED,
    DISPID_SGRSAddRuleTransition, SAFT44kHz8BitMono, SRSEDone,
    DISPID_SPERequiredConfidence, ISpeechCustomStream,
    DISPID_SGRsItem, SGRSTTDictation, ISpeechPhraseElements,
    SPPS_RESERVED1, DISPID_SRCBookmark, DISPID_SVEWord,
    SPSERIALIZEDRESULT, DISPID_SDKGetlongValue,
    DISPID_SVEventInterests, DISPID_SOTCGetDataKey,
    ISpeechVoiceStatus, ISpeechDataKey, ISpeechPhraseRules,
    DISPID_SOTDataKey, DISPID_SRRecognizer, eLEXTYPE_PRIVATE16,
    SPEI_PHONEME, SAFTCCITT_ALaw_22kHzMono, SVF_Emphasis,
    SPGS_EXCLUSIVE, DISPID_SRRGetXMLErrorInfo,
    DISPID_SAFSetWaveFormatEx, ISpeechRecoResultTimes,
    DISPID_SRCERecognition, DISPID_SDKSetLongValue,
    DISPID_SRCEHypothesis, DISPID_SVSPhonemeId,
    IInternetSecurityManager, SpTextSelectionInformation,
    ISpeechPhraseInfo, SPRST_INACTIVE, SAFT11kHz16BitMono,
    SP_VISEME_12, DISPID_SVPause, DISPID_SRCEFalseRecognition,
    ISpeechFileStream, DISPID_SRRDiscardResultInfo, SpMMAudioIn,
    SP_VISEME_21, ULONG_PTR, DISPID_SRCEEndStream,
    DISPID_SRRPhraseInfo, DISPID_SFSClose, SECNormalConfidence,
    DISPID_SMSAMMHandle, SRESoundEnd, SGRSTTTextBuffer,
    SpeechPropertyLowConfidenceThreshold, SAFT11kHz8BitMono,
    SPAS_PAUSE, DISPID_SLGetWords, SVEPhoneme,
    SPINTERFERENCE_LATENCY_TRUNCATE_BEGIN, eLEXTYPE_PRIVATE19,
    SAFTCCITT_ALaw_11kHzMono, SAFTCCITT_uLaw_22kHzMono,
    DISPID_SPRulesItem, SRCS_Disabled, SAFT8kHz8BitStereo,
    DISPID_SWFEExtraData, SAFT12kHz16BitStereo, SVSFParseMask, SVP_15,
    DISPID_SDKDeleteKey, DISPID_SRGetPropertyString, SVP_20,
    SAFTCCITT_uLaw_44kHzStereo, SPGS_ENABLED, SPSInterjection,
    DISPID_SPEAudioSizeBytes, DISPID_SRGReset, DISPID_SGRsAdd,
    SDA_Consume_Leading_Spaces, SpeechGrammarTagUnlimitedDictation,
    DISPID_SVEVoiceChange, SVSFVoiceMask, DISPID_SVStatus,
    DISPID_SPPName, DISPID_SLWsCount, DISPID_SPEs_NewEnum,
    WAVEFORMATEX, DISPID_SVSInputSentenceLength,
    ISpObjectTokenCategory, SGLexicalNoSpecialChars,
    SVEEndInputStream, DISPID_SPEAudioSizeTime, SGRSTTEpsilon,
    DISPID_SRRTLength, ISpLexicon, SP_VISEME_5, STSF_FlagCreate,
    DISPID_SABufferNotifySize, SpObjectToken,
    DISPID_SRGCmdSetRuleIdState, SPFM_CREATE, SpeechMicTraining,
    SITooSlow, eLEXTYPE_PRIVATE13, DISPID_SGRName, SpAudioFormat,
    DISPID_SRCEBookmark, STSF_LocalAppData, eLEXTYPE_USER_SHORTCUT,
    SpInProcRecoContext, SAFTCCITT_uLaw_22kHzStereo,
    SAFTCCITT_uLaw_8kHzStereo, SPSHORTCUTPAIR, SPEI_SENTENCE_BOUNDARY,
    typelib_path, SDKLDefaultLocation, DISPID_SMSGetData,
    SP_VISEME_16, SPEI_END_SR_STREAM, COMMETHOD, SPCS_ENABLED,
    SAFT32kHz16BitMono, SpeechCategoryRecognizers, SVP_10,
    SDTAlternates, SRAImport, SPXRO_Alternates_SML, SPEI_RESERVED1,
    DISPID_SRGCommit, DISPID_SPPFirstElement, eLEXTYPE_PRIVATE17,
    DISPID_SGRSRule, IInternetSecurityMgrSite,
    SAFTCCITT_uLaw_11kHzStereo, SPRST_ACTIVE,
    DISPID_SPIEnginePrivateData, SPEI_RESERVED2, SpeechTokenKeyUI,
    SPSMF_SRGS_SAPIPROPERTIES, SVP_2, SASRun, DISPID_SASNonBlockingIO,
    ISpeechGrammarRule, SPINTERFERENCE_TOOFAST,
    SpeechGrammarTagDictation, SPPHRASEPROPERTY, SGRSTTWord,
    DISPID_SGRAddResource, SpeechAddRemoveWord, SAFTNonStandardFormat,
    ISpeechMemoryStream, DISPID_SRGCmdLoadFromFile, SDKLCurrentConfig,
    SpVoice, SPVPRI_OVER, SRSActiveAlways,
    SPWT_LEXICAL_NO_SPECIAL_CHARS, _ISpeechRecoContextEvents,
    DISPID_SRCEPhraseStart, SECFIgnoreKanaType, DISPID_SRAudioInput,
    _ULARGE_INTEGER, helpstring, DISPID_SRCRequestedUIType,
    SpeechPropertyResourceUsage, DISPID_SGRs_NewEnum,
    DISPID_SRCEAdaptation, SLTApp, SECFIgnoreCase, DISPID_SGRSTType,
    SPEI_MIN_TTS, DISPIDSPTSI_SelectionLength,
    SpeechCategoryRecoProfiles, SpSharedRecoContext,
    DISPID_SGRSTransitions, DISPID_SVEBookmark, ISpStreamFormat,
    SVSFIsFilename, DISPID_SRRGetXMLResult, ISpXMLRecoResult,
    eLEXTYPE_PRIVATE7, SRTSMLTimeout, SAFT22kHz16BitStereo,
    DISPID_SPIElements, ISpShortcut, SpeechPropertyAdaptationOn,
    SPSModifier, tagSPTEXTSELECTIONINFO,
    DISPID_SGRSAddSpecialTransition, DISPID_SRSetPropertyString,
    SPEI_RECOGNITION, DISPID_SGRAddState, SECFEmulateResult,
    ISpNotifyTranslator, SP_VISEME_8, VARIANT, SPRULE, SFTInput,
    DISPID_SAFType, DISPID_SPEDisplayAttributes, SSTTDictation,
    SAFTCCITT_ALaw_44kHzMono, DISPID_SRGCmdSetRuleState,
    DISPID_SPIGetText, SVP_3, SRAInterpreter,
    SAFTCCITT_ALaw_8kHzStereo, SVSFParseSsml, ISpAudio,
    SDKLLocalMachine, DISPID_SPEEngineConfidence,
    DISPID_SPARecoResult, SPLO_STATIC, DISPID_SVRate, SP_VISEME_7,
    SAFT11kHz8BitStereo, DISPID_SRCPause, ISpeechRecognizer,
    Speech_Max_Word_Length, SPPHRASEREPLACEMENT, SpMMAudioOut,
    VARIANT_BOOL, SSSPTRelativeToCurrentPosition,
    DISPID_SRSetPropertyNumber, SAFT22kHz8BitMono, SPEI_SOUND_START,
    SAFT12kHz16BitMono, DISPID_SRCState, SPLO_DYNAMIC, SVP_8, SINoise,
    DISPID_SVGetVoices, SpeechTokenValueCLSID, SVPOver,
    SVSFParseAutodetect, DISPID_SRCRetainedAudio, ISpeechAudio,
    SRSInactive, eLEXTYPE_RESERVED10, SWPUnknownWordUnpronounceable,
    DISPID_SPACommit, SPAUDIOSTATUS, SpeechDictationTopicSpelling,
    SpeechPropertyComplexResponseSpeed, SPWORDLIST,
    DISPID_SRCreateRecoContext, SpeechVoiceCategoryTTSRate,
    SAFT22kHz8BitStereo, SINone, DISPID_SVEEnginePrivate,
    DISPID_SPPChildren, SpeechRegistryLocalMachineRoot,
    ISpNotifySource, SPWT_PRONUNCIATION, SVESentenceBoundary,
    DISPID_SBSRead, _ISpeechVoiceEvents, SPRS_ACTIVE_WITH_AUTO_PAUSE,
    SREAudioLevel, SPEI_SR_RETAINEDAUDIO, eLEXTYPE_PRIVATE18,
    DISPID_SLWType, DISPID_SPERetainedSizeBytes,
    SAFTExtendedAudioFormat, eLEXTYPE_PRIVATE2, SAFT8kHz8BitMono,
    ISpPhrase, SVP_14, SAFT44kHz16BitMono, DISPID_SPCIdToPhone,
    DISPID_SRCRecognizer, SPAR_Unknown, SVEPrivate,
    DISPID_SPAPhraseInfo, DISPID_SAEventHandle, SP_VISEME_11,
    SVEVoiceChange, DISPID_SGRAttributes,
    DISPID_SVAllowAudioOuputFormatChangesOnNextSet, SVSFNLPMask,
    DISPID_SVSRunningState, SPBO_NONE, SpNullPhoneConverter,
    SpeechPropertyHighConfidenceThreshold, SVP_13, STCRemoteServer,
    DISPID_SRSNumberOfActiveRules, SVP_11, DISPID_SOTs_NewEnum,
    SPCT_DICTATION, SAFT32kHz8BitStereo, SASClosed, SASStop,
    SDTPronunciation, eLEXTYPE_RESERVED7, STCAll, DISPID_SGRClear,
    SRTExtendableParse, DISPID_SVSyncronousSpeakTimeout,
    DISPID_SLWsItem, SREStreamStart, ISpeechMMSysAudio,
    SPPS_SuppressWord, IUnknown, DISPID_SLPLangId, DISPID_SVSpeak,
    DISPID_SRCResume, SPEI_VISEME, SAFTDefault,
    DISPID_SPRNumberOfElements, DISPID_SRCAudioInInterferenceStatus,
    DISPID_SRRRecoContext, SPAR_High, DISPID_SRGIsPronounceable,
    ISpVoice, DISPID_SRRTimes, DISPID_SRGDictationLoad, SGLexical,
    DISPID_SDKEnumKeys, SPCT_SLEEP, SBOPause, SPPS_Noncontent,
    SPRECOGNIZERSTATUS, SPEI_SR_AUDIO_LEVEL, STCInprocServer,
    DISPID_SPAsItem, DISPID_SASFreeBufferSpace, ISpSerializeState,
    ISpRecognizer, SRTStandard, ISpRecoGrammar2, DISPID_SDKOpenKey,
    SPWT_LEXICAL, SREStateChange, SPAO_RETAIN_AUDIO,
    DISPID_SOTCDefault, SPBO_AHEAD, ISpNotifySink,
    SAFTGSM610_44kHzMono, SPSHT_EMAIL, SpSharedRecognizer,
    SPEI_SOUND_END, DISPID_SRGDictationUnload, STCLocalServer,
    STSF_CommonAppData, SAFT16kHz16BitStereo, _check_version,
    DISPID_SWFEChannels, SREBookmark, SpeechUserTraining,
    DISPID_SOTCategory, SPAS_STOP, DISPID_SRRAudioFormat,
    DISPID_SAFGetWaveFormatEx, DISPID_SRStatus,
    DISPID_SVAudioOutputStream, eLEXTYPE_PRIVATE6,
    DISPID_SPRuleNumberOfElements, SREPhraseStart,
    DISPID_SRSSupportedLanguages, Speech_Default_Weight,
    SpeechAudioProperties, DISPID_SRCEPropertyStringChange,
    ISpResourceManager, DISPID_SPPNumberOfElements, SECFIgnoreWidth,
    DISPID_SVAudioOutput, ISpeechPhraseProperty, SDTAudio, SPSLMA,
    ISpeechLexicon, SpeechRecoProfileProperties, SAFT32kHz8BitMono,
    DISPID_SRCCmdMaxAlternates, DISPID_SRGetPropertyNumber,
    SP_VISEME_15, ISpeechGrammarRuleState, SITooFast, SPEI_UNDEFINED,
    ISpeechAudioBufferInfo, SDTAll, SpShortcut, SVPNormal, SVP_19,
    SPEI_ACTIVE_CATEGORY_CHANGED, DISPID_SPERetainedStreamOffset,
    SPEI_RESERVED5, SVP_21, DISPID_SPEActualConfidence,
    SSFMCreateForWrite, ISpPhoneticAlphabetConverter,
    ISpeechAudioStatus, DISPID_SPEsItem, eLEXTYPE_PRIVATE12,
    DISPID_SVSCurrentStreamNumber, SVP_7, DISPID_SPIStartTime,
    SRTReSent, ISpeechLexiconPronunciation, ISpStreamFormatConverter,
    DISPID_SPEAudioStreamOffset, SPINTERFERENCE_NOISE,
    SAFT48kHz16BitStereo, SSTTTextBuffer, SSSPTRelativeToEnd,
    DISPID_SRAudioInputStream, IStream, DISPID_SPPParent,
    SAFT11kHz16BitStereo, DISPID_SOTGetStorageFileName, ISpMMSysAudio,
    DISPID_SVEViseme, DISPID_SRSAudioStatus, STCInprocHandler,
    SECFNoSpecialChars, SPCS_DISABLED, SAFT8kHz16BitStereo,
    DISPID_SVEPhoneme, DISPID_SPISaveToMemory, SRESoundStart,
    DISPID_SPIRule, SAFT16kHz16BitMono, SpeechRegistryUserRoot,
    DISPID_SVSVisemeId, eLEXTYPE_PRIVATE10, DISPID_SPRs_NewEnum,
    DISPID_SPRText, SVSFDefault, SPINTERFERENCE_LATENCY_TRUNCATE_END,
    SDKLCurrentUser, DISPID_SPPConfidence, DISPID_SLWLangId,
    DISPID_SPRuleChildren, ISpeechPhraseProperties, SVP_12, SPPHRASE,
    DISPID_SOTRemove, ISpeechObjectTokenCategory,
    DISPID_SRAllowAudioInputFormatChangesOnNextSet,
    DISPID_SDKGetStringValue, SPSFunction, SGDSActive,
    SPRST_NUM_STATES, DISPID_SWFEBitsPerSample,
    DISPID_SGRsCommitAndSave, SRTAutopause, SGSDisabled,
    DISPID_SOTCreateInstance, DISPID_SLPPartOfSpeech,
    SPFM_CREATE_ALWAYS, SP_VISEME_4, DISPID_SPIGrammarId, dispid,
    SpeechGrammarTagWildcard, ISpGrammarBuilder, ISpeechXMLRecoResult,
    DISPID_SGRSTNextState, ISpeechGrammarRuleStateTransition,
    DISPID_SRRTStreamTime, DISPID_SLPsItem,
    DISPID_SRCRetainedAudioFormat, DISPID_SLPsCount,
    SAFTCCITT_ALaw_22kHzStereo, SP_VISEME_0, DISPID_SGRsDynamic,
    DISPID_SVGetAudioOutputs, SPWP_UNKNOWN_WORD_PRONOUNCEABLE,
    SPRECORESULTTIMES, DISPID_SPPEngineConfidence,
    SPEI_PROPERTY_STRING_CHANGE, DISPID_SASetState, ISpStream,
    DISPID_SOTCSetId, ISpeechPhraseAlternate, SPGS_DISABLED,
    SPPS_Unknown, DISPID_SRRTTickCount, eLEXTYPE_PRIVATE9,
    DISPID_SLPs_NewEnum, SPINTERFERENCE_TOOSLOW, SpFileStream,
    DISPID_SPRuleId, SAFTADPCM_8kHzStereo, SpPhraseInfoBuilder,
    WSTRING, DISPID_SVSkip, ISpEventSink, DISPID_SRGRecoContext,
    DISPID_SVESentenceBoundary, ISpeechBaseStream,
    DISPID_SRCCreateResultFromMemory, DISPID_SRGCmdLoadFromResource,
    ISpeechObjectToken, SpStreamFormatConverter, SPEI_ADAPTATION,
    ISpObjectWithToken, SGDSInactive, SECHighConfidence,
    eLEXTYPE_RESERVED8, DISPID_SPIGetDisplayAttributes,
    SGDSActiveWithAutoPause, Library, DISPID_SWFEFormatTag,
    DISPID_SPEPronunciation, SVPAlert, DISPID_SBSSeek, SRADynamic,
    SAFT48kHz8BitMono, SAFT16kHz8BitMono, DISPID_SVSLastBookmark,
    DISPID_SLAddPronunciationByPhoneIds, eLEXTYPE_LETTERTOSOUND,
    DISPID_SRRAudio, DISPID_SPRuleFirstElement,
    SAFTCCITT_uLaw_44kHzMono, SAFTTrueSpeech_8kHz1BitMono,
    SPEI_INTERFERENCE, SAFTNoAssignedFormat,
    SWPKnownWordPronounceable, SpeechCategoryVoices,
    SpeechTokenKeyFiles, DISPID_SAFGuid, ISpObjectToken,
    DISPID_SABIEventBias, SAFT8kHz16BitMono, SPSHORTCUTPAIRLIST,
    DISPID_SDKSetBinaryValue, SREHypothesis, SAFT16kHz8BitStereo,
    DISPID_SGRSTs_NewEnum, DISPID_SPIRetainedSizeBytes,
    SPSNotOverriden, tagSTATSTG, SSTTWildcard, DISPID_SOTsCount,
    SPEI_RECO_STATE_CHANGE, DISPID_SPILanguageId,
    DISPID_SPIProperties, ISpRecognizer2, SPVPRI_ALERT, DISPMETHOD,
    DISPID_SPELexicalForm, SPSEMANTICERRORINFO,
    SPINTERFERENCE_NOSIGNAL, SPDKL_LocalMachine,
    SpeechPropertyResponseSpeed, eLEXTYPE_PRIVATE15, SVP_18,
    SRSActive, ISpPhoneticAlphabetSelection, SVSFIsNotXML,
    SpUnCompressedLexicon, SVP_1, DISPID_SRCEEnginePrivate,
    SREAllEvents, DISPID_SRSClsidEngine, DISPID_SRRSaveToMemory,
    DISPID_SPRsItem, ISpeechRecoContext, SASPause,
    IEnumSpObjectTokens, eLEXTYPE_RESERVED9,
    ISpeechGrammarRuleStateTransitions, DISPID_SPEAudioTimeOffset,
    SVP_5, STSF_AppData, SGDSActiveUserDelimited,
    DISPID_SVSInputWordLength, DISPID_SPPsItem, DISPID_SPPs_NewEnum,
    SWTAdded, SPSMF_SRGS_SEMANTICINTERPRETATION_MS,
    Speech_Max_Pron_Length, SREStreamEnd, SAFTGSM610_11kHzMono,
    SpStream, SRSEIsSpeaking, DISPID_SPCPhoneToId, SPSVerb,
    _RemotableHandle, SP_VISEME_3, SITooQuiet,
    DISPID_SRGSetWordSequenceData, SRARoot, SPWT_DISPLAY,
    SPBINARYGRAMMAR, DISPID_SDKEnumValues, eLEXTYPE_PRIVATE5, SVP_9,
    DISPID_SDKCreateKey, SAFT24kHz8BitStereo, DISPID_SRGId,
    ISpRecoContext2, SPFM_OPEN_READONLY, SpeechCategoryAppLexicons,
    SGDisplay, SPBO_PAUSE, DISPID_SGRSTPropertyName,
    DISPID_SVEStreamEnd, ISpeechPhraseRule, DISPID_SGRsCommit,
    SAFT24kHz16BitMono, SPEI_FALSE_RECOGNITION, SpMemoryStream,
    SPFM_NUM_MODES, ISpeechLexiconWord, SPRS_ACTIVE_USER_DELIMITED,
    DISPID_SRCESoundStart, DISPID_SPAsCount, SP_VISEME_17,
    DISPID_SLWWord, DISPID_SWFEAvgBytesPerSec,
    DISPID_SPIAudioStreamPosition, DISPID_SRGetRecognizers, SPSNoun,
    SGSEnabled, ISpeechPhoneConverter, DISPID_SGRId, SPAO_NONE,
    DISPID_SDKSetStringValue, SGRSTTRule, SVEWordBoundary,
    eLEXTYPE_PRIVATE11, SAFTGSM610_8kHzMono,
    DISPID_SASCurrentSeekPosition, DISPID_SGRsCount,
    ISpeechAudioFormat, DISPID_SVEAudioLevel, DISPID_SVDisplayUI,
    SVP_6, _lcid, IEnumString, SLTUser, DISPID_SPRuleParent,
    ISpProperties, DISPID_SPRuleEngineConfidence, eLEXTYPE_PRIVATE4,
    IServiceProvider, SPEI_END_INPUT_STREAM,
    ISpeechPhraseReplacements, DISPID_SLGenerationId,
    DISPID_SPIEngineId, SREAdaptation, SAFTCCITT_ALaw_11kHzStereo,
    DISPID_SRIsUISupported, DISPID_SLRemovePronunciationByPhoneIds,
    DISPID_SOTSetId, DISPID_SVAlertBoundary, DISPID_SVSLastBookmarkId,
    DISPID_SGRSTWeight, SPEI_PROPERTY_NUM_CHANGE,
    DISPID_SRSCurrentStreamNumber, SAFTCCITT_uLaw_8kHzMono,
    DISPID_SVSLastResult, ISpRecognizer3, DISPID_SOTsItem,
    SPWF_SRENGINE, eLEXTYPE_MORPHOLOGY,
    ISpeechTextSelectionInformation, SPEI_RESERVED3, SFTSREngine,
    SP_VISEME_10, SPINTERFERENCE_TOOLOUD, DISPID_SWFEBlockAlign,
    SPAS_RUN, DISPID_SPAStartElementInResult, SGSExclusive,
    tagSPPROPERTYINFO, DISPID_SLPType, SPPS_Function,
    SPSHT_NotOverriden, eLEXTYPE_PRIVATE20, DISPID_SGRSTsItem,
    DISPID_SOTGetAttribute, DISPID_SVSInputSentencePosition,
    DISPID_SPRDisplayAttributes, DISPID_SOTGetDescription,
    SPEI_TTS_AUDIO_LEVEL, wireHWND, SVP_17, CoClass, SRERequestUI,
    SAFT22kHz16BitMono, DISPID_SRGCmdLoadFromProprietaryGrammar,
    DISPID_SLPPhoneIds, DISPID_SRSCurrentStreamPosition,
    DISPID_SPIReplacements, SpNotifyTranslator, DISPID_SAStatus,
    DISPID_SGRSTText, SAFTCCITT_uLaw_11kHzMono, ISpeechPhraseElement,
    SpeechAudioVolume, DISPID_SPANumberOfElementsInResult,
    SAFT24kHz16BitStereo, DISPID_SPRulesCount, DISPID_SPRsCount,
    ISpeechRecoResult2, DISPID_SPRuleName, SRAExport, ISpRecoResult,
    SP_VISEME_13, SVSFNLPSpeakPunc, eLEXTYPE_RESERVED4,
    SPEI_PHRASE_START, DISPID_SABIMinNotification, SDTLexicalForm,
    SECLowConfidence, SPEI_START_SR_STREAM, _LARGE_INTEGER,
    SAFTADPCM_22kHzStereo, DISPID_SRGSetTextSelection,
    DISPID_SABIBufferSize, DISPID_SPCLangId, DISPID_SADefaultFormat,
    SpeechEngineProperties, DISPID_SRIsShared,
    DISPID_SRRSetTextFeedback, DISPID_SLRemovePronunciation,
    SPSERIALIZEDPHRASE, LONG_PTR,
    __MIDL___MIDL_itf_sapi_0000_0020_0002,
    DISPID_SRCEPropertyNumberChange, SPCT_SUB_COMMAND,
    SAFT48kHz16BitMono, DISPID_SRCSetAdaptationData, SPPS_Verb,
    HRESULT, SRAONone, DISPID_SGRsFindRule, ISpeechRecoGrammar,
    SPRECOCONTEXTSTATUS, ISpPhraseAlt, SVP_0, ISpeechRecoResult,
    SpPhoneConverter, SPFM_OPEN_READWRITE, SPSSuppressWord, UINT_PTR,
    ISpRecoGrammar, DISPID_SGRSAddWordTransition,
    SPSMF_SAPI_PROPERTIES, SPVPRI_NORMAL, ISpeechRecognizerStatus,
    __MIDL_IWinTypes_0009, SPDKL_DefaultLocation, SVSFPersistXML,
    SAFTCCITT_ALaw_44kHzStereo, DISPID_SOTId, SVSFlagsAsync,
    SPCT_COMMAND, DISPID_SLWPronunciations, SPSUnknown,
    DISPIDSPTSI_ActiveOffset, DISPID_SRProfile, DISPID_SFSOpen,
    DISPID_SLAddPronunciation, SPEI_MAX_SR, SDA_One_Trailing_Space,
    SPRS_INACTIVE, SDA_Two_Trailing_Spaces, SDTProperty,
    SSFMOpenReadWrite, SAFTADPCM_11kHzStereo, SpMMAudioEnum,
    SLOStatic, DISPID_SRCVoicePurgeEvent, SAFTADPCM_22kHzMono,
    SPWP_UNKNOWN_WORD_UNPRONOUNCEABLE, eLEXTYPE_PRIVATE8,
    SRAORetainAudio, DISPID_SRCCreateGrammar, SPEI_HYPOTHESIS,
    SPEI_RESERVED6, SVEAudioLevel, ISpEventSource,
    SSSPTRelativeToStart, SECFDefault, DISPID_SRGCmdLoadFromObject,
    SVEAllEvents, SPRST_INACTIVE_WITH_PURGE, DISPID_SWFESamplesPerSec,
    DISPID_SRDisplayUI, ISpDataKey, SP_VISEME_19, SPPS_RESERVED2,
    DISPID_SPRuleConfidence, DISPID_SMSSetData, SAFTADPCM_11kHzMono,
    DISPID_SDKGetBinaryValue, SpObjectTokenCategory, _FILETIME,
    SVSFIsXML, SVP_4, SDTRule, SP_VISEME_2, SPCT_SUB_DICTATION,
    SAFT44kHz16BitStereo, ISpeechPhraseInfoBuilder, ISpRecoCategory,
    DISPID_SBSFormat, DISPID_SPEsCount, SpeechAllElements,
    DISPID_SOTIsUISupported, SVF_Stressed,
    SpPhoneticAlphabetConverter, DISPID_SRRSpeakAudio, SRERecognition,
    SRSInactiveWithPurge
)


class SpeechDisplayAttributes(IntFlag):
    SDA_No_Trailing_Space = 0
    SDA_One_Trailing_Space = 2
    SDA_Two_Trailing_Spaces = 4
    SDA_Consume_Leading_Spaces = 8


class SPDATAKEYLOCATION(IntFlag):
    SPDKL_DefaultLocation = 0
    SPDKL_CurrentUser = 1
    SPDKL_LocalMachine = 2
    SPDKL_CurrentConfig = 5


class SpeechStreamSeekPositionType(IntFlag):
    SSSPTRelativeToStart = 0
    SSSPTRelativeToCurrentPosition = 1
    SSSPTRelativeToEnd = 2


class SpeechAudioState(IntFlag):
    SASClosed = 0
    SASStop = 1
    SASPause = 2
    SASRun = 3


class SpeechVoiceEvents(IntFlag):
    SVEStartInputStream = 2
    SVEEndInputStream = 4
    SVEVoiceChange = 8
    SVEBookmark = 16
    SVEWordBoundary = 32
    SVEPhoneme = 64
    SVESentenceBoundary = 128
    SVEViseme = 256
    SVEAudioLevel = 512
    SVEPrivate = 32768
    SVEAllEvents = 33790


class SpeechVoicePriority(IntFlag):
    SVPNormal = 0
    SVPAlert = 1
    SVPOver = 2


class SpeechVoiceSpeakFlags(IntFlag):
    SVSFDefault = 0
    SVSFlagsAsync = 1
    SVSFPurgeBeforeSpeak = 2
    SVSFIsFilename = 4
    SVSFIsXML = 8
    SVSFIsNotXML = 16
    SVSFPersistXML = 32
    SVSFNLPSpeakPunc = 64
    SVSFParseSapi = 128
    SVSFParseSsml = 256
    SVSFParseAutodetect = 0
    SVSFNLPMask = 64
    SVSFParseMask = 384
    SVSFVoiceMask = 511
    SVSFUnusedFlags = -512


class SPGRAMMARWORDTYPE(IntFlag):
    SPWT_DISPLAY = 0
    SPWT_LEXICAL = 1
    SPWT_PRONUNCIATION = 2
    SPWT_LEXICAL_NO_SPECIAL_CHARS = 3


class SPLOADOPTIONS(IntFlag):
    SPLO_STATIC = 0
    SPLO_DYNAMIC = 1


class SPRULESTATE(IntFlag):
    SPRS_INACTIVE = 0
    SPRS_ACTIVE = 1
    SPRS_ACTIVE_WITH_AUTO_PAUSE = 3
    SPRS_ACTIVE_USER_DELIMITED = 4


class SPWORDPRONOUNCEABLE(IntFlag):
    SPWP_UNKNOWN_WORD_UNPRONOUNCEABLE = 0
    SPWP_UNKNOWN_WORD_PRONOUNCEABLE = 1
    SPWP_KNOWN_WORD_PRONOUNCEABLE = 2


class SPGRAMMARSTATE(IntFlag):
    SPGS_DISABLED = 0
    SPGS_ENABLED = 1
    SPGS_EXCLUSIVE = 3


class SPPARTOFSPEECH(IntFlag):
    SPPS_NotOverriden = -1
    SPPS_Unknown = 0
    SPPS_Noun = 4096
    SPPS_Verb = 8192
    SPPS_Modifier = 12288
    SPPS_Function = 16384
    SPPS_Interjection = 20480
    SPPS_Noncontent = 24576
    SPPS_LMA = 28672
    SPPS_SuppressWord = 61440


class SPADAPTATIONRELEVANCE(IntFlag):
    SPAR_Unknown = 0
    SPAR_Low = 1
    SPAR_Medium = 2
    SPAR_High = 3


class SpeechVisemeFeature(IntFlag):
    SVF_None = 0
    SVF_Stressed = 1
    SVF_Emphasis = 2


class SpeechVisemeType(IntFlag):
    SVP_0 = 0
    SVP_1 = 1
    SVP_2 = 2
    SVP_3 = 3
    SVP_4 = 4
    SVP_5 = 5
    SVP_6 = 6
    SVP_7 = 7
    SVP_8 = 8
    SVP_9 = 9
    SVP_10 = 10
    SVP_11 = 11
    SVP_12 = 12
    SVP_13 = 13
    SVP_14 = 14
    SVP_15 = 15
    SVP_16 = 16
    SVP_17 = 17
    SVP_18 = 18
    SVP_19 = 19
    SVP_20 = 20
    SVP_21 = 21


class SpeechWordPronounceable(IntFlag):
    SWPUnknownWordUnpronounceable = 0
    SWPUnknownWordPronounceable = 1
    SWPKnownWordPronounceable = 2


class DISPID_SpeechRecoResult(IntFlag):
    DISPID_SRRRecoContext = 1
    DISPID_SRRTimes = 2
    DISPID_SRRAudioFormat = 3
    DISPID_SRRPhraseInfo = 4
    DISPID_SRRAlternates = 5
    DISPID_SRRAudio = 6
    DISPID_SRRSpeakAudio = 7
    DISPID_SRRSaveToMemory = 8
    DISPID_SRRDiscardResultInfo = 9


class SpeechLexiconType(IntFlag):
    SLTUser = 1
    SLTApp = 2


class SpeechPartOfSpeech(IntFlag):
    SPSNotOverriden = -1
    SPSUnknown = 0
    SPSNoun = 4096
    SPSVerb = 8192
    SPSModifier = 12288
    SPSFunction = 16384
    SPSInterjection = 20480
    SPSLMA = 28672
    SPSSuppressWord = 61440


class SPFILEMODE(IntFlag):
    SPFM_OPEN_READONLY = 0
    SPFM_OPEN_READWRITE = 1
    SPFM_CREATE = 2
    SPFM_CREATE_ALWAYS = 3
    SPFM_NUM_MODES = 4


class SpeechDiscardType(IntFlag):
    SDTProperty = 1
    SDTReplacement = 2
    SDTRule = 4
    SDTDisplayText = 8
    SDTLexicalForm = 16
    SDTPronunciation = 32
    SDTAudio = 64
    SDTAlternates = 128
    SDTAll = 255


class SPSEMANTICFORMAT(IntFlag):
    SPSMF_SAPI_PROPERTIES = 0
    SPSMF_SRGS_SEMANTICINTERPRETATION_MS = 1
    SPSMF_SRGS_SAPIPROPERTIES = 2
    SPSMF_UPS = 4
    SPSMF_SRGS_SEMANTICINTERPRETATION_W3C = 8


class SPRECOSTATE(IntFlag):
    SPRST_INACTIVE = 0
    SPRST_ACTIVE = 1
    SPRST_ACTIVE_ALWAYS = 2
    SPRST_INACTIVE_WITH_PURGE = 3
    SPRST_NUM_STATES = 4


class SPWAVEFORMATTYPE(IntFlag):
    SPWF_INPUT = 0
    SPWF_SRENGINE = 1


class SPWORDTYPE(IntFlag):
    eWORDTYPE_ADDED = 1
    eWORDTYPE_DELETED = 2


class SPVPRIORITY(IntFlag):
    SPVPRI_NORMAL = 0
    SPVPRI_ALERT = 1
    SPVPRI_OVER = 2


class SPEVENTENUM(IntFlag):
    SPEI_UNDEFINED = 0
    SPEI_START_INPUT_STREAM = 1
    SPEI_END_INPUT_STREAM = 2
    SPEI_VOICE_CHANGE = 3
    SPEI_TTS_BOOKMARK = 4
    SPEI_WORD_BOUNDARY = 5
    SPEI_PHONEME = 6
    SPEI_SENTENCE_BOUNDARY = 7
    SPEI_VISEME = 8
    SPEI_TTS_AUDIO_LEVEL = 9
    SPEI_TTS_PRIVATE = 15
    SPEI_MIN_TTS = 1
    SPEI_MAX_TTS = 15
    SPEI_END_SR_STREAM = 34
    SPEI_SOUND_START = 35
    SPEI_SOUND_END = 36
    SPEI_PHRASE_START = 37
    SPEI_RECOGNITION = 38
    SPEI_HYPOTHESIS = 39
    SPEI_SR_BOOKMARK = 40
    SPEI_PROPERTY_NUM_CHANGE = 41
    SPEI_PROPERTY_STRING_CHANGE = 42
    SPEI_FALSE_RECOGNITION = 43
    SPEI_INTERFERENCE = 44
    SPEI_REQUEST_UI = 45
    SPEI_RECO_STATE_CHANGE = 46
    SPEI_ADAPTATION = 47
    SPEI_START_SR_STREAM = 48
    SPEI_RECO_OTHER_CONTEXT = 49
    SPEI_SR_AUDIO_LEVEL = 50
    SPEI_SR_RETAINEDAUDIO = 51
    SPEI_SR_PRIVATE = 52
    SPEI_ACTIVE_CATEGORY_CHANGED = 53
    SPEI_RESERVED5 = 54
    SPEI_RESERVED6 = 55
    SPEI_MIN_SR = 34
    SPEI_MAX_SR = 55
    SPEI_RESERVED1 = 30
    SPEI_RESERVED2 = 33
    SPEI_RESERVED3 = 63


class SpeechWordType(IntFlag):
    SWTAdded = 1
    SWTDeleted = 2


class SpeechRunState(IntFlag):
    SRSEDone = 1
    SRSEIsSpeaking = 2


class SPBOOKMARKOPTIONS(IntFlag):
    SPBO_NONE = 0
    SPBO_PAUSE = 1
    SPBO_AHEAD = 2
    SPBO_TIME_UNITS = 4


class SPSHORTCUTTYPE(IntFlag):
    SPSHT_NotOverriden = -1
    SPSHT_Unknown = 0
    SPSHT_EMAIL = 4096
    SPSHT_OTHER = 8192
    SPPS_RESERVED1 = 12288
    SPPS_RESERVED2 = 16384
    SPPS_RESERVED3 = 20480
    SPPS_RESERVED4 = 61440


class SpeechBookmarkOptions(IntFlag):
    SBONone = 0
    SBOPause = 1


class SpeechFormatType(IntFlag):
    SFTInput = 0
    SFTSREngine = 1


class SpeechEngineConfidence(IntFlag):
    SECLowConfidence = -1
    SECNormalConfidence = 0
    SECHighConfidence = 1


class SPCONTEXTSTATE(IntFlag):
    SPCS_DISABLED = 0
    SPCS_ENABLED = 1


class SPVISEMES(IntFlag):
    SP_VISEME_0 = 0
    SP_VISEME_1 = 1
    SP_VISEME_2 = 2
    SP_VISEME_3 = 3
    SP_VISEME_4 = 4
    SP_VISEME_5 = 5
    SP_VISEME_6 = 6
    SP_VISEME_7 = 7
    SP_VISEME_8 = 8
    SP_VISEME_9 = 9
    SP_VISEME_10 = 10
    SP_VISEME_11 = 11
    SP_VISEME_12 = 12
    SP_VISEME_13 = 13
    SP_VISEME_14 = 14
    SP_VISEME_15 = 15
    SP_VISEME_16 = 16
    SP_VISEME_17 = 17
    SP_VISEME_18 = 18
    SP_VISEME_19 = 19
    SP_VISEME_20 = 20
    SP_VISEME_21 = 21


class SPCATEGORYTYPE(IntFlag):
    SPCT_COMMAND = 0
    SPCT_DICTATION = 1
    SPCT_SLEEP = 2
    SPCT_SUB_COMMAND = 3
    SPCT_SUB_DICTATION = 4


class SPXMLRESULTOPTIONS(IntFlag):
    SPXRO_SML = 0
    SPXRO_Alternates_SML = 1


class SPLEXICONTYPE(IntFlag):
    eLEXTYPE_USER = 1
    eLEXTYPE_APP = 2
    eLEXTYPE_VENDORLEXICON = 4
    eLEXTYPE_LETTERTOSOUND = 8
    eLEXTYPE_MORPHOLOGY = 16
    eLEXTYPE_RESERVED4 = 32
    eLEXTYPE_USER_SHORTCUT = 64
    eLEXTYPE_RESERVED6 = 128
    eLEXTYPE_RESERVED7 = 256
    eLEXTYPE_RESERVED8 = 512
    eLEXTYPE_RESERVED9 = 1024
    eLEXTYPE_RESERVED10 = 2048
    eLEXTYPE_PRIVATE1 = 4096
    eLEXTYPE_PRIVATE2 = 8192
    eLEXTYPE_PRIVATE3 = 16384
    eLEXTYPE_PRIVATE4 = 32768
    eLEXTYPE_PRIVATE5 = 65536
    eLEXTYPE_PRIVATE6 = 131072
    eLEXTYPE_PRIVATE7 = 262144
    eLEXTYPE_PRIVATE8 = 524288
    eLEXTYPE_PRIVATE9 = 1048576
    eLEXTYPE_PRIVATE10 = 2097152
    eLEXTYPE_PRIVATE11 = 4194304
    eLEXTYPE_PRIVATE12 = 8388608
    eLEXTYPE_PRIVATE13 = 16777216
    eLEXTYPE_PRIVATE14 = 33554432
    eLEXTYPE_PRIVATE15 = 67108864
    eLEXTYPE_PRIVATE16 = 134217728
    eLEXTYPE_PRIVATE17 = 268435456
    eLEXTYPE_PRIVATE18 = 536870912
    eLEXTYPE_PRIVATE19 = 1073741824
    eLEXTYPE_PRIVATE20 = -2147483648


class DISPID_SpeechObjectToken(IntFlag):
    DISPID_SOTId = 1
    DISPID_SOTDataKey = 2
    DISPID_SOTCategory = 3
    DISPID_SOTGetDescription = 4
    DISPID_SOTSetId = 5
    DISPID_SOTGetAttribute = 6
    DISPID_SOTCreateInstance = 7
    DISPID_SOTRemove = 8
    DISPID_SOTGetStorageFileName = 9
    DISPID_SOTRemoveStorageFileName = 10
    DISPID_SOTIsUISupported = 11
    DISPID_SOTDisplayUI = 12
    DISPID_SOTMatchesAttributes = 13


class DISPID_SpeechDataKey(IntFlag):
    DISPID_SDKSetBinaryValue = 1
    DISPID_SDKGetBinaryValue = 2
    DISPID_SDKSetStringValue = 3
    DISPID_SDKGetStringValue = 4
    DISPID_SDKSetLongValue = 5
    DISPID_SDKGetlongValue = 6
    DISPID_SDKOpenKey = 7
    DISPID_SDKCreateKey = 8
    DISPID_SDKDeleteKey = 9
    DISPID_SDKDeleteValue = 10
    DISPID_SDKEnumKeys = 11
    DISPID_SDKEnumValues = 12


class SPINTERFERENCE(IntFlag):
    SPINTERFERENCE_NONE = 0
    SPINTERFERENCE_NOISE = 1
    SPINTERFERENCE_NOSIGNAL = 2
    SPINTERFERENCE_TOOLOUD = 3
    SPINTERFERENCE_TOOQUIET = 4
    SPINTERFERENCE_TOOFAST = 5
    SPINTERFERENCE_TOOSLOW = 6
    SPINTERFERENCE_LATENCY_WARNING = 7
    SPINTERFERENCE_LATENCY_TRUNCATE_BEGIN = 8
    SPINTERFERENCE_LATENCY_TRUNCATE_END = 9


class DISPID_SpeechObjectTokens(IntFlag):
    DISPID_SOTsCount = 1
    DISPID_SOTsItem = 0
    DISPID_SOTs_NewEnum = -4


class SpeechRecognizerState(IntFlag):
    SRSInactive = 0
    SRSActive = 1
    SRSActiveAlways = 2
    SRSInactiveWithPurge = 3


class SpeechRecognitionType(IntFlag):
    SRTStandard = 0
    SRTAutopause = 1
    SRTEmulated = 2
    SRTSMLTimeout = 4
    SRTExtendableParse = 8
    SRTReSent = 16


class SPAUDIOOPTIONS(IntFlag):
    SPAO_NONE = 0
    SPAO_RETAIN_AUDIO = 1


class DISPID_SpeechGrammarRules(IntFlag):
    DISPID_SGRsCount = 1
    DISPID_SGRsDynamic = 2
    DISPID_SGRsAdd = 3
    DISPID_SGRsCommit = 4
    DISPID_SGRsCommitAndSave = 5
    DISPID_SGRsFindRule = 6
    DISPID_SGRsItem = 0
    DISPID_SGRs_NewEnum = -4


class DISPIDSPRG(IntFlag):
    DISPID_SRGId = 1
    DISPID_SRGRecoContext = 2
    DISPID_SRGState = 3
    DISPID_SRGRules = 4
    DISPID_SRGReset = 5
    DISPID_SRGCommit = 6
    DISPID_SRGCmdLoadFromFile = 7
    DISPID_SRGCmdLoadFromObject = 8
    DISPID_SRGCmdLoadFromResource = 9
    DISPID_SRGCmdLoadFromMemory = 10
    DISPID_SRGCmdLoadFromProprietaryGrammar = 11
    DISPID_SRGCmdSetRuleState = 12
    DISPID_SRGCmdSetRuleIdState = 13
    DISPID_SRGDictationLoad = 14
    DISPID_SRGDictationUnload = 15
    DISPID_SRGDictationSetState = 16
    DISPID_SRGSetWordSequenceData = 17
    DISPID_SRGSetTextSelection = 18
    DISPID_SRGIsPronounceable = 19


class DISPID_SpeechObjectTokenCategory(IntFlag):
    DISPID_SOTCId = 1
    DISPID_SOTCDefault = 2
    DISPID_SOTCSetId = 3
    DISPID_SOTCGetDataKey = 4
    DISPID_SOTCEnumerateTokens = 5


class DISPID_SpeechAudioFormat(IntFlag):
    DISPID_SAFType = 1
    DISPID_SAFGuid = 2
    DISPID_SAFGetWaveFormatEx = 3
    DISPID_SAFSetWaveFormatEx = 4


class SpeechAudioFormatType(IntFlag):
    SAFTDefault = -1
    SAFTNoAssignedFormat = 0
    SAFTText = 1
    SAFTNonStandardFormat = 2
    SAFTExtendedAudioFormat = 3
    SAFT8kHz8BitMono = 4
    SAFT8kHz8BitStereo = 5
    SAFT8kHz16BitMono = 6
    SAFT8kHz16BitStereo = 7
    SAFT11kHz8BitMono = 8
    SAFT11kHz8BitStereo = 9
    SAFT11kHz16BitMono = 10
    SAFT11kHz16BitStereo = 11
    SAFT12kHz8BitMono = 12
    SAFT12kHz8BitStereo = 13
    SAFT12kHz16BitMono = 14
    SAFT12kHz16BitStereo = 15
    SAFT16kHz8BitMono = 16
    SAFT16kHz8BitStereo = 17
    SAFT16kHz16BitMono = 18
    SAFT16kHz16BitStereo = 19
    SAFT22kHz8BitMono = 20
    SAFT22kHz8BitStereo = 21
    SAFT22kHz16BitMono = 22
    SAFT22kHz16BitStereo = 23
    SAFT24kHz8BitMono = 24
    SAFT24kHz8BitStereo = 25
    SAFT24kHz16BitMono = 26
    SAFT24kHz16BitStereo = 27
    SAFT32kHz8BitMono = 28
    SAFT32kHz8BitStereo = 29
    SAFT32kHz16BitMono = 30
    SAFT32kHz16BitStereo = 31
    SAFT44kHz8BitMono = 32
    SAFT44kHz8BitStereo = 33
    SAFT44kHz16BitMono = 34
    SAFT44kHz16BitStereo = 35
    SAFT48kHz8BitMono = 36
    SAFT48kHz8BitStereo = 37
    SAFT48kHz16BitMono = 38
    SAFT48kHz16BitStereo = 39
    SAFTTrueSpeech_8kHz1BitMono = 40
    SAFTCCITT_ALaw_8kHzMono = 41
    SAFTCCITT_ALaw_8kHzStereo = 42
    SAFTCCITT_ALaw_11kHzMono = 43
    SAFTCCITT_ALaw_11kHzStereo = 44
    SAFTCCITT_ALaw_22kHzMono = 45
    SAFTCCITT_ALaw_22kHzStereo = 46
    SAFTCCITT_ALaw_44kHzMono = 47
    SAFTCCITT_ALaw_44kHzStereo = 48
    SAFTCCITT_uLaw_8kHzMono = 49
    SAFTCCITT_uLaw_8kHzStereo = 50
    SAFTCCITT_uLaw_11kHzMono = 51
    SAFTCCITT_uLaw_11kHzStereo = 52
    SAFTCCITT_uLaw_22kHzMono = 53
    SAFTCCITT_uLaw_22kHzStereo = 54
    SAFTCCITT_uLaw_44kHzMono = 55
    SAFTCCITT_uLaw_44kHzStereo = 56
    SAFTADPCM_8kHzMono = 57
    SAFTADPCM_8kHzStereo = 58
    SAFTADPCM_11kHzMono = 59
    SAFTADPCM_11kHzStereo = 60
    SAFTADPCM_22kHzMono = 61
    SAFTADPCM_22kHzStereo = 62
    SAFTADPCM_44kHzMono = 63
    SAFTADPCM_44kHzStereo = 64
    SAFTGSM610_8kHzMono = 65
    SAFTGSM610_11kHzMono = 66
    SAFTGSM610_22kHzMono = 67
    SAFTGSM610_44kHzMono = 68


class SpeechGrammarState(IntFlag):
    SGSEnabled = 1
    SGSDisabled = 0
    SGSExclusive = 3


class DISPID_SpeechBaseStream(IntFlag):
    DISPID_SBSFormat = 1
    DISPID_SBSRead = 2
    DISPID_SBSWrite = 3
    DISPID_SBSSeek = 4


class DISPID_SpeechPhraseElement(IntFlag):
    DISPID_SPEAudioTimeOffset = 1
    DISPID_SPEAudioSizeTime = 2
    DISPID_SPEAudioStreamOffset = 3
    DISPID_SPEAudioSizeBytes = 4
    DISPID_SPERetainedStreamOffset = 5
    DISPID_SPERetainedSizeBytes = 6
    DISPID_SPEDisplayText = 7
    DISPID_SPELexicalForm = 8
    DISPID_SPEPronunciation = 9
    DISPID_SPEDisplayAttributes = 10
    DISPID_SPERequiredConfidence = 11
    DISPID_SPEActualConfidence = 12
    DISPID_SPEEngineConfidence = 13


class DISPID_SpeechAudio(IntFlag):
    DISPID_SAStatus = 200
    DISPID_SABufferInfo = 201
    DISPID_SADefaultFormat = 202
    DISPID_SAVolume = 203
    DISPID_SABufferNotifySize = 204
    DISPID_SAEventHandle = 205
    DISPID_SASetState = 206


class SpeechTokenContext(IntFlag):
    STCInprocServer = 1
    STCInprocHandler = 2
    STCLocalServer = 4
    STCRemoteServer = 16
    STCAll = 23


class SpeechTokenShellFolder(IntFlag):
    STSF_AppData = 26
    STSF_LocalAppData = 28
    STSF_CommonAppData = 35
    STSF_FlagCreate = 32768


class DISPID_SpeechRecoContextEvents(IntFlag):
    DISPID_SRCEStartStream = 1
    DISPID_SRCEEndStream = 2
    DISPID_SRCEBookmark = 3
    DISPID_SRCESoundStart = 4
    DISPID_SRCESoundEnd = 5
    DISPID_SRCEPhraseStart = 6
    DISPID_SRCERecognition = 7
    DISPID_SRCEHypothesis = 8
    DISPID_SRCEPropertyNumberChange = 9
    DISPID_SRCEPropertyStringChange = 10
    DISPID_SRCEFalseRecognition = 11
    DISPID_SRCEInterference = 12
    DISPID_SRCERequestUI = 13
    DISPID_SRCERecognizerStateChange = 14
    DISPID_SRCEAdaptation = 15
    DISPID_SRCERecognitionForOtherContext = 16
    DISPID_SRCEAudioLevel = 17
    DISPID_SRCEEnginePrivate = 18


class SpeechRuleAttributes(IntFlag):
    SRATopLevel = 1
    SRADefaultToActive = 2
    SRAExport = 4
    SRAImport = 8
    SRAInterpreter = 16
    SRADynamic = 32
    SRARoot = 64


class DISPID_SpeechMMSysAudio(IntFlag):
    DISPID_SMSADeviceId = 300
    DISPID_SMSALineId = 301
    DISPID_SMSAMMHandle = 302


class _SPAUDIOSTATE(IntFlag):
    SPAS_CLOSED = 0
    SPAS_STOP = 1
    SPAS_PAUSE = 2
    SPAS_RUN = 3


class DISPID_SpeechPhraseElements(IntFlag):
    DISPID_SPEsCount = 1
    DISPID_SPEsItem = 0
    DISPID_SPEs_NewEnum = -4


class DISPID_SpeechFileStream(IntFlag):
    DISPID_SFSOpen = 100
    DISPID_SFSClose = 101


class DISPID_SpeechCustomStream(IntFlag):
    DISPID_SCSBaseStream = 100


class DISPID_SpeechPhraseReplacement(IntFlag):
    DISPID_SPRDisplayAttributes = 1
    DISPID_SPRText = 2
    DISPID_SPRFirstElement = 3
    DISPID_SPRNumberOfElements = 4


class DISPID_SpeechMemoryStream(IntFlag):
    DISPID_SMSSetData = 100
    DISPID_SMSGetData = 101


class DISPID_SpeechAudioStatus(IntFlag):
    DISPID_SASFreeBufferSpace = 1
    DISPID_SASNonBlockingIO = 2
    DISPID_SASState = 3
    DISPID_SASCurrentSeekPosition = 4
    DISPID_SASCurrentDevicePosition = 5


class DISPID_SpeechPhraseReplacements(IntFlag):
    DISPID_SPRsCount = 1
    DISPID_SPRsItem = 0
    DISPID_SPRs_NewEnum = -4


class DISPID_SpeechGrammarRule(IntFlag):
    DISPID_SGRAttributes = 1
    DISPID_SGRInitialState = 2
    DISPID_SGRName = 3
    DISPID_SGRId = 4
    DISPID_SGRClear = 5
    DISPID_SGRAddResource = 6
    DISPID_SGRAddState = 7


class DISPID_SpeechPhraseProperty(IntFlag):
    DISPID_SPPName = 1
    DISPID_SPPId = 2
    DISPID_SPPValue = 3
    DISPID_SPPFirstElement = 4
    DISPID_SPPNumberOfElements = 5
    DISPID_SPPEngineConfidence = 6
    DISPID_SPPConfidence = 7
    DISPID_SPPParent = 8
    DISPID_SPPChildren = 9


class DISPID_SpeechAudioBufferInfo(IntFlag):
    DISPID_SABIMinNotification = 1
    DISPID_SABIBufferSize = 2
    DISPID_SABIEventBias = 3


class SpeechGrammarWordType(IntFlag):
    SGDisplay = 0
    SGLexical = 1
    SGPronounciation = 2
    SGLexicalNoSpecialChars = 3


class SpeechSpecialTransitionType(IntFlag):
    SSTTWildcard = 1
    SSTTDictation = 2
    SSTTTextBuffer = 3


class DISPID_SpeechWaveFormatEx(IntFlag):
    DISPID_SWFEFormatTag = 1
    DISPID_SWFEChannels = 2
    DISPID_SWFESamplesPerSec = 3
    DISPID_SWFEAvgBytesPerSec = 4
    DISPID_SWFEBlockAlign = 5
    DISPID_SWFEBitsPerSample = 6
    DISPID_SWFEExtraData = 7


class DISPID_SpeechPhraseProperties(IntFlag):
    DISPID_SPPsCount = 1
    DISPID_SPPsItem = 0
    DISPID_SPPs_NewEnum = -4


class DISPID_SpeechGrammarRuleState(IntFlag):
    DISPID_SGRSRule = 1
    DISPID_SGRSTransitions = 2
    DISPID_SGRSAddWordTransition = 3
    DISPID_SGRSAddRuleTransition = 4
    DISPID_SGRSAddSpecialTransition = 5


class DISPID_SpeechVoice(IntFlag):
    DISPID_SVStatus = 1
    DISPID_SVVoice = 2
    DISPID_SVAudioOutput = 3
    DISPID_SVAudioOutputStream = 4
    DISPID_SVRate = 5
    DISPID_SVVolume = 6
    DISPID_SVAllowAudioOuputFormatChangesOnNextSet = 7
    DISPID_SVEventInterests = 8
    DISPID_SVPriority = 9
    DISPID_SVAlertBoundary = 10
    DISPID_SVSyncronousSpeakTimeout = 11
    DISPID_SVSpeak = 12
    DISPID_SVSpeakStream = 13
    DISPID_SVPause = 14
    DISPID_SVResume = 15
    DISPID_SVSkip = 16
    DISPID_SVGetVoices = 17
    DISPID_SVGetAudioOutputs = 18
    DISPID_SVWaitUntilDone = 19
    DISPID_SVSpeakCompleteEvent = 20
    DISPID_SVIsUISupported = 21
    DISPID_SVDisplayUI = 22


class DISPID_SpeechPhraseRule(IntFlag):
    DISPID_SPRuleName = 1
    DISPID_SPRuleId = 2
    DISPID_SPRuleFirstElement = 3
    DISPID_SPRuleNumberOfElements = 4
    DISPID_SPRuleParent = 5
    DISPID_SPRuleChildren = 6
    DISPID_SPRuleConfidence = 7
    DISPID_SPRuleEngineConfidence = 8


class DISPID_SpeechGrammarRuleStateTransitions(IntFlag):
    DISPID_SGRSTsCount = 1
    DISPID_SGRSTsItem = 0
    DISPID_SGRSTs_NewEnum = -4


class DISPID_SpeechGrammarRuleStateTransition(IntFlag):
    DISPID_SGRSTType = 1
    DISPID_SGRSTText = 2
    DISPID_SGRSTRule = 3
    DISPID_SGRSTWeight = 4
    DISPID_SGRSTPropertyName = 5
    DISPID_SGRSTPropertyId = 6
    DISPID_SGRSTPropertyValue = 7
    DISPID_SGRSTNextState = 8


class DISPID_SpeechPhraseRules(IntFlag):
    DISPID_SPRulesCount = 1
    DISPID_SPRulesItem = 0
    DISPID_SPRules_NewEnum = -4


class SpeechGrammarRuleStateTransitionType(IntFlag):
    SGRSTTEpsilon = 0
    SGRSTTWord = 1
    SGRSTTRule = 2
    SGRSTTDictation = 3
    SGRSTTWildcard = 4
    SGRSTTTextBuffer = 5


class DISPID_SpeechLexicon(IntFlag):
    DISPID_SLGenerationId = 1
    DISPID_SLGetWords = 2
    DISPID_SLAddPronunciation = 3
    DISPID_SLAddPronunciationByPhoneIds = 4
    DISPID_SLRemovePronunciation = 5
    DISPID_SLRemovePronunciationByPhoneIds = 6
    DISPID_SLGetPronunciations = 7
    DISPID_SLGetGenerationChange = 8


class DISPIDSPTSI(IntFlag):
    DISPIDSPTSI_ActiveOffset = 1
    DISPIDSPTSI_ActiveLength = 2
    DISPIDSPTSI_SelectionOffset = 3
    DISPIDSPTSI_SelectionLength = 4


class DISPID_SpeechLexiconWords(IntFlag):
    DISPID_SLWsCount = 1
    DISPID_SLWsItem = 0
    DISPID_SLWs_NewEnum = -4


class DISPID_SpeechVoiceStatus(IntFlag):
    DISPID_SVSCurrentStreamNumber = 1
    DISPID_SVSLastStreamNumberQueued = 2
    DISPID_SVSLastResult = 3
    DISPID_SVSRunningState = 4
    DISPID_SVSInputWordPosition = 5
    DISPID_SVSInputWordLength = 6
    DISPID_SVSInputSentencePosition = 7
    DISPID_SVSInputSentenceLength = 8
    DISPID_SVSLastBookmark = 9
    DISPID_SVSLastBookmarkId = 10
    DISPID_SVSPhonemeId = 11
    DISPID_SVSVisemeId = 12


class DISPID_SpeechLexiconWord(IntFlag):
    DISPID_SLWLangId = 1
    DISPID_SLWType = 2
    DISPID_SLWWord = 3
    DISPID_SLWPronunciations = 4


class DISPID_SpeechXMLRecoResult(IntFlag):
    DISPID_SRRGetXMLResult = 10
    DISPID_SRRGetXMLErrorInfo = 11


class DISPID_SpeechLexiconProns(IntFlag):
    DISPID_SLPsCount = 1
    DISPID_SLPsItem = 0
    DISPID_SLPs_NewEnum = -4


class SpeechInterference(IntFlag):
    SINone = 0
    SINoise = 1
    SINoSignal = 2
    SITooLoud = 3
    SITooQuiet = 4
    SITooFast = 5
    SITooSlow = 6


class SpeechRecoEvents(IntFlag):
    SREStreamEnd = 1
    SRESoundStart = 2
    SRESoundEnd = 4
    SREPhraseStart = 8
    SRERecognition = 16
    SREHypothesis = 32
    SREBookmark = 64
    SREPropertyNumChange = 128
    SREPropertyStringChange = 256
    SREFalseRecognition = 512
    SREInterference = 1024
    SRERequestUI = 2048
    SREStateChange = 4096
    SREAdaptation = 8192
    SREStreamStart = 16384
    SRERecoOtherContext = 32768
    SREAudioLevel = 65536
    SREPrivate = 262144
    SREAllEvents = 393215


class SpeechRecoContextState(IntFlag):
    SRCS_Disabled = 0
    SRCS_Enabled = 1


class SpeechRetainedAudioOptions(IntFlag):
    SRAONone = 0
    SRAORetainAudio = 1


class DISPID_SpeechLexiconPronunciation(IntFlag):
    DISPID_SLPType = 1
    DISPID_SLPLangId = 2
    DISPID_SLPPartOfSpeech = 3
    DISPID_SLPPhoneIds = 4
    DISPID_SLPSymbolic = 5


class DISPID_SpeechVoiceEvent(IntFlag):
    DISPID_SVEStreamStart = 1
    DISPID_SVEStreamEnd = 2
    DISPID_SVEVoiceChange = 3
    DISPID_SVEBookmark = 4
    DISPID_SVEWord = 5
    DISPID_SVEPhoneme = 6
    DISPID_SVESentenceBoundary = 7
    DISPID_SVEViseme = 8
    DISPID_SVEAudioLevel = 9
    DISPID_SVEEnginePrivate = 10


class DISPID_SpeechPhoneConverter(IntFlag):
    DISPID_SPCLangId = 1
    DISPID_SPCPhoneToId = 2
    DISPID_SPCIdToPhone = 3


class DISPID_SpeechRecoResult2(IntFlag):
    DISPID_SRRSetTextFeedback = 12


class DISPID_SpeechRecognizer(IntFlag):
    DISPID_SRRecognizer = 1
    DISPID_SRAllowAudioInputFormatChangesOnNextSet = 2
    DISPID_SRAudioInput = 3
    DISPID_SRAudioInputStream = 4
    DISPID_SRIsShared = 5
    DISPID_SRState = 6
    DISPID_SRStatus = 7
    DISPID_SRProfile = 8
    DISPID_SREmulateRecognition = 9
    DISPID_SRCreateRecoContext = 10
    DISPID_SRGetFormat = 11
    DISPID_SRSetPropertyNumber = 12
    DISPID_SRGetPropertyNumber = 13
    DISPID_SRSetPropertyString = 14
    DISPID_SRGetPropertyString = 15
    DISPID_SRIsUISupported = 16
    DISPID_SRDisplayUI = 17
    DISPID_SRGetRecognizers = 18
    DISPID_SVGetAudioInputs = 19
    DISPID_SVGetProfiles = 20


class SpeechDataKeyLocation(IntFlag):
    SDKLDefaultLocation = 0
    SDKLCurrentUser = 1
    SDKLLocalMachine = 2
    SDKLCurrentConfig = 5


class DISPID_SpeechPhraseInfo(IntFlag):
    DISPID_SPILanguageId = 1
    DISPID_SPIGrammarId = 2
    DISPID_SPIStartTime = 3
    DISPID_SPIAudioStreamPosition = 4
    DISPID_SPIAudioSizeBytes = 5
    DISPID_SPIRetainedSizeBytes = 6
    DISPID_SPIAudioSizeTime = 7
    DISPID_SPIRule = 8
    DISPID_SPIProperties = 9
    DISPID_SPIElements = 10
    DISPID_SPIReplacements = 11
    DISPID_SPIEngineId = 12
    DISPID_SPIEnginePrivateData = 13
    DISPID_SPISaveToMemory = 14
    DISPID_SPIGetText = 15
    DISPID_SPIGetDisplayAttributes = 16


class SpeechEmulationCompareFlags(IntFlag):
    SECFIgnoreCase = 1
    SECFIgnoreKanaType = 65536
    SECFIgnoreWidth = 131072
    SECFNoSpecialChars = 536870912
    SECFEmulateResult = 1073741824
    SECFDefault = 196609


class DISPID_SpeechRecoResultTimes(IntFlag):
    DISPID_SRRTStreamTime = 1
    DISPID_SRRTLength = 2
    DISPID_SRRTTickCount = 3
    DISPID_SRRTOffsetFromStart = 4


class DISPID_SpeechRecognizerStatus(IntFlag):
    DISPID_SRSAudioStatus = 1
    DISPID_SRSCurrentStreamPosition = 2
    DISPID_SRSCurrentStreamNumber = 3
    DISPID_SRSNumberOfActiveRules = 4
    DISPID_SRSClsidEngine = 5
    DISPID_SRSSupportedLanguages = 6


class DISPID_SpeechPhraseBuilder(IntFlag):
    DISPID_SPPBRestorePhraseFromMemory = 1


class SpeechStreamFileMode(IntFlag):
    SSFMOpenForRead = 0
    SSFMOpenReadWrite = 1
    SSFMCreate = 2
    SSFMCreateForWrite = 3


class SpeechLoadOption(IntFlag):
    SLOStatic = 0
    SLODynamic = 1


class DISPID_SpeechRecoContext(IntFlag):
    DISPID_SRCRecognizer = 1
    DISPID_SRCAudioInInterferenceStatus = 2
    DISPID_SRCRequestedUIType = 3
    DISPID_SRCVoice = 4
    DISPID_SRAllowVoiceFormatMatchingOnNextSet = 5
    DISPID_SRCVoicePurgeEvent = 6
    DISPID_SRCEventInterests = 7
    DISPID_SRCCmdMaxAlternates = 8
    DISPID_SRCState = 9
    DISPID_SRCRetainedAudio = 10
    DISPID_SRCRetainedAudioFormat = 11
    DISPID_SRCPause = 12
    DISPID_SRCResume = 13
    DISPID_SRCCreateGrammar = 14
    DISPID_SRCCreateResultFromMemory = 15
    DISPID_SRCBookmark = 16
    DISPID_SRCSetAdaptationData = 17


class SpeechRuleState(IntFlag):
    SGDSInactive = 0
    SGDSActive = 1
    SGDSActiveWithAutoPause = 3
    SGDSActiveUserDelimited = 4


class DISPID_SpeechPhraseAlternate(IntFlag):
    DISPID_SPARecoResult = 1
    DISPID_SPAStartElementInResult = 2
    DISPID_SPANumberOfElementsInResult = 3
    DISPID_SPAPhraseInfo = 4
    DISPID_SPACommit = 5


class DISPID_SpeechPhraseAlternates(IntFlag):
    DISPID_SPAsCount = 1
    DISPID_SPAsItem = 0
    DISPID_SPAs_NewEnum = -4


SPSTREAMFORMATTYPE = SPWAVEFORMATTYPE
SPAUDIOSTATE = _SPAUDIOSTATE


__all__ = [
    'SpeechVoiceSpeakFlags', 'DISPID_SRGState', 'SPEI_WORD_BOUNDARY',
    'SRTStandard', 'ISpRecoGrammar2', 'SDA_No_Trailing_Space',
    'DISPID_SDKOpenKey', 'SPBOOKMARKOPTIONS', 'SPWT_LEXICAL',
    'SREStateChange', 'DISPID_SVResume', 'SPSEMANTICFORMAT',
    'SPAO_RETAIN_AUDIO', 'DISPID_SOTCDefault', 'SPBO_AHEAD',
    'SAFTGSM610_22kHzMono', 'ISpNotifySink', 'SAFTGSM610_44kHzMono',
    'DISPID_SVSLastStreamNumberQueued', 'SPSHT_EMAIL',
    'SpSharedRecognizer', 'SPEI_SOUND_END', 'SPDKL_CurrentConfig',
    'DISPID_SPEDisplayText', 'DISPID_SVEStreamStart',
    'ISpeechGrammarRules', 'DISPID_SLPSymbolic', 'SPEI_MIN_SR',
    'SPSHT_Unknown', 'DISPID_SRGDictationUnload', 'STCLocalServer',
    'SpeechDisplayAttributes', 'SPSHT_OTHER', 'SpeechCategoryAudioIn',
    'STSF_CommonAppData', 'SDTReplacement', 'SAFT16kHz16BitStereo',
    'DISPID_SWFEChannels', 'SREBookmark', 'SpeechUserTraining',
    'DISPID_SOTMatchesAttributes', 'DISPID_SMSADeviceId',
    'eLEXTYPE_PRIVATE3', 'eLEXTYPE_PRIVATE14',
    'DISPID_SRRAudioFormat', 'SpeechAudioFormatGUIDWave',
    'DISPID_SOTCategory', 'eWORDTYPE_DELETED',
    'DISPID_SDKDeleteValue', 'SPAS_STOP', 'DISPID_SGRSTRule',
    'SpeechCategoryAudioOut',
    'DISPID_SpeechGrammarRuleStateTransition', 'SPPS_RESERVED3',
    'DISPID_SAFGetWaveFormatEx', 'SPAR_Low', 'SWTDeleted',
    'SP_VISEME_20', 'Speech_StreamPos_RealTime',
    'DISPID_SGRSTPropertyId', 'SpWaveFormatEx', 'SPDKL_CurrentUser',
    'SREPrivate', 'SPRS_ACTIVE', 'DISPID_SVAudioOutputStream',
    'eLEXTYPE_PRIVATE6', 'DISPID_SRStatus', 'SLODynamic',
    'SpeechAudioFormatGUIDText', 'DISPID_SpeechPhraseElements',
    'SVP_16', 'DISPID_SLGetGenerationChange', 'SPVOICESTATUS',
    'SPEVENTSOURCEINFO', 'SpeechFormatType', 'SVSFParseSapi',
    'DISPID_SRGDictationSetState', 'DISPID_SOTCEnumerateTokens',
    'DISPID_SPRuleNumberOfElements', 'SREPhraseStart',
    'DISPID_SVSpeakCompleteEvent', 'SpeechAudioFormatType',
    'SPEI_RECO_OTHER_CONTEXT', 'DISPID_SRSSupportedLanguages',
    'eLEXTYPE_RESERVED6', 'SpeechAudioProperties',
    'Speech_Default_Weight', 'SAFT12kHz8BitMono',
    'DISPID_SRRTOffsetFromStart', 'DISPID_SRCEPropertyStringChange',
    'ISpResourceManager', 'SRATopLevel',
    'SWPUnknownWordPronounceable', 'DISPID_SOTRemoveStorageFileName',
    'SRERecoOtherContext', 'SPTEXTSELECTIONINFO', 'SPAR_Medium',
    'DISPID_SPPNumberOfElements', 'DISPID_SVSInputWordPosition',
    'DISPID_SREmulateRecognition', 'SPWORDPRONUNCIATION',
    'DISPID_SGRSTPropertyValue', 'SpeechRecoContextState',
    'SECFIgnoreWidth', 'DISPID_SVAudioOutput',
    'ISpeechPhraseProperty', 'SP_VISEME_1', 'SDTAudio',
    'SAFTADPCM_44kHzMono', 'SPSLMA', 'DISPID_SVVoice',
    '__MIDL___MIDL_itf_sapi_0000_0020_0001', 'ISpeechLexicon',
    'SpLexicon', 'SpeechRecoProfileProperties', 'SPEI_TTS_PRIVATE',
    'SAFT32kHz8BitMono', 'DISPID_SRCCmdMaxAlternates',
    'DISPID_SRGetPropertyNumber', 'DISPID_SASCurrentDevicePosition',
    'SP_VISEME_15', 'SpeechDataKeyLocation', 'SAFT48kHz8BitStereo',
    'SPGRAMMARWORDTYPE', 'ISpeechGrammarRuleState',
    'DISPID_SpeechPhraseAlternates', 'SITooFast', 'DISPID_SRGRules',
    'SPEI_UNDEFINED', 'SPAUDIOBUFFERINFO', 'ISpeechAudioBufferInfo',
    'DISPID_SPPsCount', 'SDTAll', 'SpShortcut', 'SVPNormal',
    'SpCustomStream', 'SPEI_VOICE_CHANGE', 'SRCS_Enabled',
    'SPWP_KNOWN_WORD_PRONOUNCEABLE', 'SVP_19', 'SPEI_REQUEST_UI',
    'SAFT44kHz8BitStereo', 'DISPID_SRRAlternates',
    'SPEI_ACTIVE_CATEGORY_CHANGED', 'DISPID_SPERetainedStreamOffset',
    'DISPID_SRState', 'SPEI_SR_BOOKMARK', 'SPEI_RESERVED5', 'SVP_21',
    'DISPID_SPEActualConfidence', 'eLEXTYPE_VENDORLEXICON',
    'SREPropertyStringChange', 'SPEVENTENUM',
    'DISPID_SpeechAudioFormat', 'SSFMCreateForWrite',
    'ISpPhoneticAlphabetConverter', 'ISpeechAudioStatus',
    'DISPID_SPEsItem', 'eLEXTYPE_PRIVATE12',
    'DISPID_SVSCurrentStreamNumber', 'SPINTERFERENCE_LATENCY_WARNING',
    'SVP_7', 'SPPS_RESERVED4', 'SPPS_Modifier', 'DISPID_SRCESoundEnd',
    'SRTReSent', 'SPEI_START_INPUT_STREAM', 'DISPID_SpeechBaseStream',
    'DISPID_SPIStartTime', 'SPXRO_SML', 'SPEI_TTS_BOOKMARK',
    'ISpeechLexiconPronunciation', 'ISpStreamFormatConverter',
    'DISPID_SPEAudioStreamOffset', 'SPINTERFERENCE_NOISE',
    'SPWORDPRONUNCIATIONLIST', 'SAFT48kHz16BitStereo',
    'DISPID_SGRInitialState', 'SSTTTextBuffer',
    'DISPID_SPIAudioSizeTime', 'SPPS_Noun',
    'SpeechCategoryPhoneConverters', 'SSFMOpenForRead', 'SPRECOSTATE',
    'DISPID_SAVolume', 'SSSPTRelativeToEnd',
    'DISPID_SRAudioInputStream', 'DISPID_SVGetProfiles', 'IStream',
    'SBONone', 'SP_VISEME_18', 'DISPID_SVSpeakStream',
    'SpeechLoadOption', 'ISpeechResourceLoader', 'DISPID_SPPParent',
    'SpeechRetainedAudioOptions', 'DISPID_SASState',
    'Speech_StreamPos_Asap', 'SAFT11kHz16BitStereo',
    'SpeechTokenIdUserLexicon', 'DISPID_SOTGetStorageFileName',
    'SPPS_Interjection', 'DISPID_SpeechCustomStream',
    'SGRSTTWildcard', 'ISpMMSysAudio', 'DISPID_SPAs_NewEnum',
    'DISPID_SVEViseme', 'DISPID_SRSAudioStatus', 'DISPID_SOTCId',
    'STCInprocHandler', 'SECFNoSpecialChars', 'SVEViseme',
    'eLEXTYPE_PRIVATE1', 'SpeechRunState', 'SPVPRIORITY',
    'SPCS_DISABLED', 'SAFT8kHz16BitStereo', 'DISPID_SMSALineId',
    'DISPID_SVEPhoneme', 'SPINTERFERENCE_NONE',
    'DISPID_SPISaveToMemory', 'SVEStartInputStream', 'SRESoundStart',
    'SAFT16kHz16BitMono', 'DISPID_SPIRule', 'SpeechRegistryUserRoot',
    'DISPID_SVSVisemeId', 'eLEXTYPE_PRIVATE10', 'DISPID_SPRs_NewEnum',
    'DISPIDSPTSI_SelectionOffset', 'DISPID_SPRText', 'SVSFDefault',
    'SpeechTokenContext', 'SPINTERFERENCE_LATENCY_TRUNCATE_END',
    'DISPID_SRCERequestUI', 'SPPS_LMA', 'SpCompressedLexicon',
    'SAFT12kHz8BitStereo', 'eLEXTYPE_APP', 'SDKLCurrentUser',
    'DISPID_SPPConfidence', 'DISPID_SRGetFormat',
    'SAFTADPCM_8kHzMono', 'SSFMCreate', 'DISPID_SLWLangId',
    'DISPID_SpeechRecoResultTimes', 'SPPHRASEELEMENT',
    'DISPID_SPRuleChildren', 'SITooLoud', 'ISpeechPhraseProperties',
    'SVP_12', 'DISPID_SpeechAudio', 'SPPS_NotOverriden',
    'ISpeechWaveFormatEx', 'DISPID_SpeechXMLRecoResult',
    'ISpeechLexiconPronunciations', 'SPPHRASE', 'SPINTERFERENCE',
    'DISPID_SOTRemove', 'ISpeechObjectTokenCategory',
    'SpeechEmulationCompareFlags', 'SPPHRASERULE', 'SPBO_TIME_UNITS',
    'DISPID_SRAllowAudioInputFormatChangesOnNextSet',
    'ISpeechLexiconWords', 'SpResourceManager',
    'DISPID_SDKGetStringValue', 'SpeechBookmarkOptions',
    'SPSFunction', 'SPWORD', 'DISPIDSPTSI', 'SGDSActive',
    'SVEBookmark', 'SPRST_NUM_STATES', 'DISPID_SWFEBitsPerSample',
    'SVSFUnusedFlags', 'DISPID_SBSWrite', 'DISPID_SRCEventInterests',
    'DISPID_SGRsCommitAndSave', 'SRTAutopause',
    'DISPID_SCSBaseStream', 'DISPID_SRCVoice', 'eWORDTYPE_ADDED',
    'ISpSerializeState', 'SPEI_SR_PRIVATE',
    'DISPID_SOTCreateInstance', 'SGSDisabled',
    'DISPID_SRCEAudioLevel', 'SPEVENT', 'SpeechTokenKeyAttributes',
    'SpeechRecoEvents', 'DISPID_SLPPartOfSpeech',
    'ISpeechPhraseReplacement', 'SPWORDTYPE', 'SP_VISEME_6',
    'SDTDisplayText', 'SAFTCCITT_ALaw_8kHzMono', 'SRTEmulated',
    'DISPID_SRCEInterference', 'ISpeechObjectTokens',
    'SPFM_CREATE_ALWAYS', 'ISpPhoneConverter',
    'DISPID_SRCEStartStream', 'SP_VISEME_4', 'DISPID_SVWaitUntilDone',
    'DISPID_SPIGrammarId', 'SPPROPERTYINFO', 'SREInterference',
    'DISPID_SGRSTsCount', 'DISPID_SpeechPhraseAlternate',
    'DISPID_SABufferInfo', 'SpeechGrammarTagWildcard',
    'ISpeechRecoResultDispatch', 'ISpGrammarBuilder',
    'DISPID_SVGetAudioInputs', 'ISpeechXMLRecoResult',
    'DISPID_SGRSTNextState', 'DISPID_SOTDisplayUI',
    'ISpeechGrammarRuleStateTransition',
    'SPSMF_SRGS_SEMANTICINTERPRETATION_W3C', 'DISPID_SRRTStreamTime',
    'DISPID_SLPsItem', 'SPINTERFERENCE_TOOQUIET',
    'SRADefaultToActive', 'DISPID_SVPriority',
    'SAFTADPCM_44kHzStereo', 'DISPID_SpeechDataKey', 'SP_VISEME_14',
    'DISPID_SRCRetainedAudioFormat', 'DISPID_SLPsCount', 'SVF_None',
    'SAFTCCITT_ALaw_22kHzStereo', 'DISPID_SPRules_NewEnum',
    'DISPID_SPPBRestorePhraseFromMemory', 'SP_VISEME_0',
    'DISPID_SGRsDynamic', 'DISPID_SVGetAudioOutputs',
    'SPWP_UNKNOWN_WORD_PRONOUNCEABLE', 'eLEXTYPE_USER',
    'SPRECORESULTTIMES', 'SpeechEngineConfidence',
    'DISPID_SPPEngineConfidence', 'SPEI_PROPERTY_STRING_CHANGE',
    'DISPID_SASetState', 'ISpStream', 'SPSMF_UPS', 'SPWF_INPUT',
    'DISPID_SOTCSetId', 'SpeechPartOfSpeech', 'DISPID_SLWs_NewEnum',
    'SPFILEMODE', 'ISpeechPhraseAlternate', 'SPGS_DISABLED',
    'SpeechVoiceSkipTypeSentence', 'SPPS_Unknown', 'DISPID_SPPValue',
    'ISpeechPhraseAlternates', 'DISPID_SRRTTickCount',
    'eLEXTYPE_PRIVATE9', 'SAFT32kHz16BitStereo',
    'DISPID_SLPs_NewEnum', 'SPINTERFERENCE_TOOSLOW', 'SpFileStream',
    'DISPID_SPRuleId', 'SREPropertyNumChange',
    'DISPID_SVIsUISupported', 'DISPID_SPIAudioSizeBytes',
    'SAFTADPCM_8kHzStereo', 'DISPID_SRCERecognizerStateChange',
    'SINoSignal', 'SpPhraseInfoBuilder', 'SREFalseRecognition',
    'SpeechRuleState', 'SPEI_MAX_TTS', 'DISPID_SVSkip',
    'SPLEXICONTYPE', 'ISpEventSink', 'DISPID_SPRFirstElement',
    'SP_VISEME_9', 'ISpRecoContext', 'DISPID_SPPId',
    'DISPID_SVVolume', 'DISPID_SpeechPhraseBuilder',
    'DISPID_SRGRecoContext', 'DISPID_SVESentenceBoundary',
    'ISpeechBaseStream', 'DISPID_SRAllowVoiceFormatMatchingOnNextSet',
    'DISPID_SRCCreateResultFromMemory',
    'DISPID_SRGCmdLoadFromResource', 'ISpeechObjectToken',
    'SpStreamFormatConverter', 'SPEI_ADAPTATION',
    'ISpObjectWithToken', 'SGDSInactive', 'SAFTText',
    'SpInprocRecognizer', 'SpeechLexiconType', 'SECHighConfidence',
    'eLEXTYPE_RESERVED8', 'DISPID_SRGCmdLoadFromMemory',
    'DISPID_SPIGetDisplayAttributes', 'SGDSActiveWithAutoPause',
    'SPRST_ACTIVE_ALWAYS', 'SpeechPropertyNormalConfidenceThreshold',
    'SVSFPurgeBeforeSpeak', 'SAFT24kHz8BitMono', 'SGPronounciation',
    'DISPIDSPTSI_ActiveLength', 'Library',
    'DISPID_SLGetPronunciations', 'DISPID_SWFEFormatTag',
    'DISPID_SRCERecognitionForOtherContext', 'ISpeechVoice',
    'DISPID_SPEPronunciation', 'SPAS_CLOSED', 'SVPAlert',
    'DISPID_SBSSeek', 'DISPID_SGRSAddRuleTransition',
    'SAFT44kHz8BitMono', 'DISPID_SpeechPhraseElement', 'SRSEDone',
    'DISPID_SPERequiredConfidence', 'ISpeechCustomStream',
    'SRADynamic', 'SAFT48kHz8BitMono', 'SAFT16kHz8BitMono',
    'DISPID_SGRsItem', 'DISPID_SVSLastBookmark',
    'DISPID_SLAddPronunciationByPhoneIds', 'eLEXTYPE_LETTERTOSOUND',
    'SGRSTTDictation', 'DISPID_SRRAudio', 'DISPID_SPRuleFirstElement',
    'ISpeechPhraseElements', 'SAFTTrueSpeech_8kHz1BitMono',
    'SPEI_INTERFERENCE', 'SPPS_RESERVED1', 'SAFTNoAssignedFormat',
    'SAFTCCITT_uLaw_44kHzMono', 'DISPID_SRCBookmark',
    'SWPKnownWordPronounceable', 'DISPID_SVEWord',
    'SPSERIALIZEDRESULT', 'SpeechCategoryVoices',
    'DISPID_SDKGetlongValue', 'DISPIDSPRG', 'DISPID_SVEventInterests',
    'SpeechGrammarWordType', 'DISPID_SOTCGetDataKey',
    'ISpeechVoiceStatus', 'ISpeechDataKey', 'ISpeechPhraseRules',
    'SpeechTokenKeyFiles', 'DISPID_SOTDataKey', 'eLEXTYPE_PRIVATE16',
    'DISPID_SRRecognizer', 'DISPID_SAFGuid', 'ISpObjectToken',
    'SPEI_PHONEME', 'DISPID_SABIEventBias', 'SAFT8kHz16BitMono',
    'SPSHORTCUTPAIRLIST', 'DISPID_SDKSetBinaryValue',
    'SAFTCCITT_ALaw_22kHzMono', 'SREHypothesis',
    'SAFT16kHz8BitStereo', 'SPWORDPRONOUNCEABLE', 'SVF_Emphasis',
    'SPGS_EXCLUSIVE', 'DISPID_SGRSTs_NewEnum',
    'DISPID_SAFSetWaveFormatEx', 'DISPID_SRRGetXMLErrorInfo',
    'DISPID_SPIRetainedSizeBytes', 'ISpeechRecoResultTimes',
    'DISPID_SRCERecognition', 'DISPID_SDKSetLongValue',
    'DISPID_SRCEHypothesis', 'DISPID_SVSPhonemeId', 'SPSNotOverriden',
    'IInternetSecurityManager', 'SpTextSelectionInformation',
    'tagSTATSTG', 'ISpeechPhraseInfo', 'SpeechTokenShellFolder',
    'SPRST_INACTIVE', 'SSTTWildcard', 'DISPID_SOTsCount',
    'SAFT11kHz16BitMono', 'DISPID_SpeechAudioStatus',
    'SPEI_RECO_STATE_CHANGE', 'DISPID_SpeechGrammarRuleState',
    'DISPID_SPILanguageId', 'SP_VISEME_12', 'DISPID_SPIProperties',
    'ISpRecognizer2', 'SPVPRI_ALERT', 'DISPID_SVPause',
    'DISPID_SRCEFalseRecognition', 'ISpeechFileStream',
    'DISPID_SRRDiscardResultInfo', 'DISPID_SPELexicalForm',
    'SpMMAudioIn', 'SP_VISEME_21', 'SPRULESTATE',
    'SPSEMANTICERRORINFO', 'SPINTERFERENCE_NOSIGNAL',
    'SPDKL_LocalMachine', 'DISPID_SRCEEndStream',
    'DISPID_SRRPhraseInfo', 'DISPID_SFSClose', 'SECNormalConfidence',
    'DISPID_SMSAMMHandle', 'SRESoundEnd', 'DISPID_SpeechPhraseRules',
    'SGRSTTTextBuffer', 'SpeechPropertyResponseSpeed',
    'SpeechPropertyLowConfidenceThreshold', 'SAFT11kHz8BitMono',
    'SPAS_PAUSE', 'DISPID_SLGetWords', 'SVEPhoneme',
    'DISPID_SpeechLexiconPronunciation', 'SpeechAudioState',
    'SPINTERFERENCE_LATENCY_TRUNCATE_BEGIN', 'eLEXTYPE_PRIVATE15',
    'SVP_18', 'SRSActive', 'eLEXTYPE_PRIVATE19',
    'ISpPhoneticAlphabetSelection', 'SAFTCCITT_ALaw_11kHzMono',
    'SVSFIsNotXML', 'SAFTCCITT_uLaw_22kHzMono',
    'SpUnCompressedLexicon', 'DISPID_SPRulesItem', 'SVP_1',
    'DISPID_SRCEEnginePrivate', 'SREAllEvents',
    'DISPID_SRSClsidEngine', 'SRCS_Disabled',
    'DISPID_SRRSaveToMemory', 'DISPID_SPRsItem', 'SAFT8kHz8BitStereo',
    'DISPID_SWFEExtraData', 'SAFT12kHz16BitStereo',
    'ISpeechRecoContext', 'SASPause', 'IEnumSpObjectTokens',
    'SVSFParseMask', 'eLEXTYPE_RESERVED9',
    'ISpeechGrammarRuleStateTransitions', 'STCInprocServer',
    'DISPID_SpeechLexiconProns', 'SVP_15', 'DISPID_SDKDeleteKey',
    'DISPID_SPEAudioTimeOffset', 'DISPID_SRGetPropertyString',
    'SVP_20', 'SVP_5', 'SPGS_ENABLED', 'SPSInterjection',
    'SAFTCCITT_uLaw_44kHzStereo', 'SpeechGrammarState',
    'STSF_AppData', 'DISPID_SPEAudioSizeBytes', 'DISPID_SRGReset',
    'SGDSActiveUserDelimited', 'DISPID_SGRsAdd',
    'SDA_Consume_Leading_Spaces',
    'SpeechGrammarTagUnlimitedDictation', 'DISPID_SVSInputWordLength',
    'DISPID_SVEVoiceChange', 'SVSFVoiceMask', 'DISPID_SPPsItem',
    'DISPID_SpeechRecoContext', 'DISPID_SPPs_NewEnum',
    'DISPID_SVStatus', 'SWTAdded',
    'SPSMF_SRGS_SEMANTICINTERPRETATION_MS', 'Speech_Max_Pron_Length',
    'SREStreamEnd', 'SAFTGSM610_11kHzMono', 'DISPID_SPPName',
    'DISPID_SpeechPhraseReplacement', 'DISPID_SLWsCount',
    'DISPID_SPEs_NewEnum', 'SpStream', 'SRSEIsSpeaking',
    'WAVEFORMATEX', 'DISPID_SVSInputSentenceLength', 'SPSVerb',
    'DISPID_SPCPhoneToId', 'SGLexicalNoSpecialChars',
    'ISpObjectTokenCategory', '_RemotableHandle',
    'SpeechVoicePriority', 'SVEEndInputStream', 'SP_VISEME_3',
    'DISPID_SPEAudioSizeTime', 'SGRSTTEpsilon', 'SITooQuiet',
    'DISPID_SRRTLength', 'DISPID_SpeechPhraseProperties',
    'ISpLexicon', 'SP_VISEME_5', 'DISPID_SRGSetWordSequenceData',
    'STSF_FlagCreate', 'SRARoot', 'SPWT_DISPLAY', 'SPBINARYGRAMMAR',
    'DISPID_SABufferNotifySize', 'SpObjectToken',
    'DISPID_SDKEnumValues', 'eLEXTYPE_PRIVATE5', 'SVP_9',
    'DISPID_SDKCreateKey', 'SAFT24kHz8BitStereo', 'DISPID_SRGId',
    'ISpRecoContext2', 'DISPID_SRGCmdSetRuleIdState', 'SPFM_CREATE',
    'SPFM_OPEN_READONLY', 'SpeechCategoryAppLexicons', 'SGDisplay',
    'SPBO_PAUSE', 'DISPID_SGRSTPropertyName', 'SpeechMicTraining',
    'DISPID_SVEStreamEnd', 'SITooSlow', 'eLEXTYPE_PRIVATE13',
    'DISPID_SGRName', 'DISPID_SpeechWaveFormatEx', 'SpAudioFormat',
    'DISPID_SRCEBookmark', 'ISpeechPhraseRule', 'STSF_LocalAppData',
    'eLEXTYPE_USER_SHORTCUT', 'SpInProcRecoContext',
    'SAFTCCITT_uLaw_22kHzStereo', 'DISPID_SGRsCommit',
    'SAFT24kHz16BitMono', 'SAFTCCITT_uLaw_8kHzStereo',
    'SPEI_FALSE_RECOGNITION', 'SPSHORTCUTPAIR', 'SpMemoryStream',
    'SPFM_NUM_MODES', 'SPEI_SENTENCE_BOUNDARY', 'ISpeechLexiconWord',
    'typelib_path', 'SPRS_ACTIVE_USER_DELIMITED',
    'DISPID_SRCESoundStart', 'SP_VISEME_16', 'SPEI_END_SR_STREAM',
    'DISPID_SMSGetData', 'SPCS_ENABLED', 'SDKLDefaultLocation',
    'DISPID_SPAsCount', 'SAFT32kHz16BitMono', 'SP_VISEME_17',
    'SpeechCategoryRecognizers', 'SVP_10', 'SDTAlternates',
    'SRAImport', 'SPXRO_Alternates_SML', 'DISPID_SWFEAvgBytesPerSec',
    'DISPID_SLWWord', 'SPSNoun', 'SGSEnabled',
    'DISPID_SRGetRecognizers', 'DISPID_SPIAudioStreamPosition',
    'SPEI_RESERVED1', 'SpeechRecognitionType', 'DISPID_SRGCommit',
    'DISPID_SPPFirstElement', 'eLEXTYPE_PRIVATE17',
    'ISpeechPhoneConverter', 'SPAUDIOSTATE', 'DISPID_SGRId',
    'DISPID_SGRSRule', 'SPAO_NONE', 'IInternetSecurityMgrSite',
    'SAFTCCITT_uLaw_11kHzStereo', 'DISPID_SDKSetStringValue',
    'DISPID_SpeechVoice', 'SGRSTTRule', 'SVEWordBoundary',
    'eLEXTYPE_PRIVATE11', 'SAFTGSM610_8kHzMono',
    'DISPID_SASCurrentSeekPosition', 'SPRST_ACTIVE',
    'DISPID_SPIEnginePrivateData', 'DISPID_SGRsCount',
    'SPEI_RESERVED2', 'SpeechTokenKeyUI', 'ISpeechAudioFormat',
    'DISPID_SVEAudioLevel', 'DISPID_SVDisplayUI',
    'SPSMF_SRGS_SAPIPROPERTIES', 'SVP_2', 'SASRun', 'SVP_6',
    'DISPID_SASNonBlockingIO', 'SpeechWordPronounceable',
    'IEnumString', 'ISpeechGrammarRule', 'SLTUser',
    'SPINTERFERENCE_TOOFAST', 'ISpProperties', 'SPSTREAMFORMATTYPE',
    'SpeechGrammarTagDictation', 'eLEXTYPE_PRIVATE4',
    'SPEI_END_INPUT_STREAM', 'DISPID_SPRuleParent',
    'DISPID_SPRuleEngineConfidence', 'ISpeechPhraseReplacements',
    'SGRSTTWord', 'DISPID_SLGenerationId', 'SPPHRASEPROPERTY',
    'DISPID_SPIEngineId', 'SREAdaptation', 'DISPID_SGRAddResource',
    'DISPID_SpeechObjectToken', 'SpeechAddRemoveWord',
    'SAFTNonStandardFormat', 'ISpeechMemoryStream',
    'DISPID_SRGCmdLoadFromFile', 'SAFTCCITT_ALaw_11kHzStereo',
    'DISPID_SpeechAudioBufferInfo', 'DISPID_SRIsUISupported',
    'DISPID_SLRemovePronunciationByPhoneIds', 'DISPID_SOTSetId',
    'SDKLCurrentConfig', 'DISPID_SVAlertBoundary',
    'SpeechDiscardType', 'DISPID_SVSLastBookmarkId', 'SpVoice',
    'DISPID_SGRSTWeight', 'SPVPRI_OVER', 'SRSActiveAlways',
    'SPWT_LEXICAL_NO_SPECIAL_CHARS', 'SPEI_PROPERTY_NUM_CHANGE',
    'DISPID_SRSCurrentStreamNumber', 'SAFTCCITT_uLaw_8kHzMono',
    'DISPID_SVSLastResult', 'ISpRecognizer3', 'SpeechVoiceEvents',
    'DISPID_SOTsItem', 'DISPID_SRCEPhraseStart',
    '_ISpeechRecoContextEvents', 'SpeechSpecialTransitionType',
    'SECFIgnoreKanaType', 'DISPID_SRAudioInput',
    'DISPID_SRCRequestedUIType', 'SpeechPropertyResourceUsage',
    'SPWF_SRENGINE', 'eLEXTYPE_MORPHOLOGY', 'DISPID_SGRs_NewEnum',
    'DISPID_SRCEAdaptation', 'ISpeechTextSelectionInformation',
    'SLTApp', 'SECFIgnoreCase', 'DISPID_SGRSTType', 'SPEI_MIN_TTS',
    'DISPIDSPTSI_SelectionLength', 'SPEI_RESERVED3',
    'SpeechCategoryRecoProfiles', 'SFTSREngine', 'SP_VISEME_10',
    'SPINTERFERENCE_TOOLOUD', 'DISPID_SWFEBlockAlign',
    'SPADAPTATIONRELEVANCE', 'DISPID_SpeechPhoneConverter',
    'SpSharedRecoContext', 'DISPID_SGRSTransitions',
    'DISPID_SVEBookmark', 'ISpStreamFormat', 'SVSFIsFilename',
    'DISPID_SRRGetXMLResult', 'ISpXMLRecoResult', 'SPAS_RUN',
    '_SPAUDIOSTATE', 'eLEXTYPE_PRIVATE7', 'SPDATAKEYLOCATION',
    'SRTSMLTimeout', 'SAFT22kHz16BitStereo', 'SpeechStreamFileMode',
    'DISPID_SPAStartElementInResult', 'SGSExclusive',
    'DISPID_SPIElements', 'tagSPPROPERTYINFO', 'ISpShortcut',
    'SPLOADOPTIONS', 'SPSModifier', 'SpeechPropertyAdaptationOn',
    'DISPID_SLPType', 'tagSPTEXTSELECTIONINFO',
    'DISPID_SpeechRecoResult2', 'DISPID_SGRSAddSpecialTransition',
    'SPPS_Function', 'SPSHT_NotOverriden', 'eLEXTYPE_PRIVATE20',
    'DISPID_SGRSTsItem', 'SPEI_RECOGNITION', 'DISPID_SOTGetAttribute',
    'DISPID_SGRAddState', 'DISPID_SpeechGrammarRule',
    'DISPID_SVSInputSentencePosition', 'DISPID_SPRDisplayAttributes',
    'DISPID_SRSetPropertyString', 'SECFEmulateResult',
    'DISPID_SOTGetDescription', 'ISpNotifyTranslator', 'SP_VISEME_8',
    'SPEI_TTS_AUDIO_LEVEL', 'DISPID_SpeechVoiceStatus', 'SPRULE',
    'SVP_17', 'SRERequestUI', 'SAFT22kHz16BitMono',
    'DISPID_SRGCmdLoadFromProprietaryGrammar', 'DISPID_SLPPhoneIds',
    'DISPID_SRSCurrentStreamPosition', 'SFTInput',
    'SpeechRuleAttributes', 'DISPID_SPIReplacements',
    'SpNotifyTranslator', 'DISPID_SAFType', 'SpeechVisemeType',
    'DISPID_SAStatus', 'DISPID_SPEDisplayAttributes',
    'DISPID_SGRSTText', 'SSTTDictation', 'DISPID_SpeechVoiceEvent',
    'SAFTCCITT_ALaw_44kHzMono', 'DISPID_SRGCmdSetRuleState',
    'SAFTCCITT_uLaw_11kHzMono', 'ISpeechPhraseElement',
    'SpeechAudioVolume', 'DISPID_SPANumberOfElementsInResult',
    'SAFT24kHz16BitStereo', 'SVP_3', 'DISPID_SPIGetText',
    'SRAInterpreter', 'DISPID_SPRulesCount', 'DISPID_SPRsCount',
    'SAFTCCITT_ALaw_8kHzStereo', 'ISpeechRecoResult2',
    'SVSFParseSsml', 'ISpAudio', 'SDKLLocalMachine',
    'DISPID_SPEEngineConfidence', 'DISPID_SPARecoResult',
    'DISPID_SPRuleName', 'SRAExport', 'ISpRecoResult', 'SPLO_STATIC',
    'DISPID_SVRate', 'SP_VISEME_13', 'SVSFNLPSpeakPunc',
    'SP_VISEME_7', 'eLEXTYPE_RESERVED4', 'SAFT11kHz8BitStereo',
    'DISPID_SpeechLexicon', 'SPAUDIOOPTIONS', 'SPEI_PHRASE_START',
    'DISPID_SABIMinNotification', 'DISPID_SpeechPhraseRule',
    'DISPID_SRCPause', 'ISpeechRecognizer', 'Speech_Max_Word_Length',
    'SDTLexicalForm', 'SPPHRASEREPLACEMENT', 'SpMMAudioOut',
    'SSSPTRelativeToCurrentPosition', 'SECLowConfidence',
    'SPEI_START_SR_STREAM', 'DISPID_SRSetPropertyNumber',
    'SpeechVisemeFeature', 'SAFTADPCM_22kHzStereo',
    'DISPID_SpeechGrammarRules', 'DISPID_SRGSetTextSelection',
    'DISPID_SABIBufferSize', 'SAFT22kHz8BitMono',
    'DISPID_SpeechPhraseProperty', 'DISPID_SPCLangId',
    'DISPID_SADefaultFormat', 'SPEI_SOUND_START',
    'SpeechEngineProperties', 'SAFT12kHz16BitMono',
    'DISPID_SRRSetTextFeedback', 'DISPID_SRIsShared',
    'SpeechWordType', 'SPLO_DYNAMIC', 'SPSERIALIZEDPHRASE', 'SVP_8',
    'LONG_PTR', 'DISPID_SLRemovePronunciation', 'SINoise',
    '__MIDL___MIDL_itf_sapi_0000_0020_0002',
    'DISPID_SpeechRecognizer', 'DISPID_SRCEPropertyNumberChange',
    'SPCT_SUB_COMMAND', 'SAFT48kHz16BitMono', 'DISPID_SVGetVoices',
    'DISPID_SRCSetAdaptationData', 'SPPS_Verb', 'DISPID_SPAsItem',
    'SRAONone', 'DISPID_SGRsFindRule', 'SpeechTokenValueCLSID',
    'SVPOver', 'SpeechStreamSeekPositionType',
    'DISPID_SpeechRecognizerStatus', 'ISpeechRecoGrammar',
    'SPRECOCONTEXTSTATUS', 'DISPID_SpeechRecoContextEvents',
    'ISpPhraseAlt', 'SVSFParseAutodetect',
    'DISPID_SpeechPhraseReplacements', 'SVP_0', 'ISpeechAudio',
    'DISPID_SRCRetainedAudio', 'ISpeechRecoResult', 'SRSInactive',
    'SpPhoneConverter', 'SPFM_OPEN_READWRITE', 'SPSSuppressWord',
    'eLEXTYPE_RESERVED10', 'SpeechInterference',
    'SWPUnknownWordUnpronounceable', 'UINT_PTR',
    'DISPID_SpeechObjectTokens', 'ISpRecoGrammar',
    'DISPID_SGRSAddWordTransition', 'DISPID_SPACommit',
    'SPPARTOFSPEECH', 'SPAUDIOSTATUS', 'DISPID_SRCResume',
    'SPSMF_SAPI_PROPERTIES', 'SPVPRI_NORMAL',
    'SpeechGrammarRuleStateTransitionType', 'ISpeechRecognizerStatus',
    '__MIDL_IWinTypes_0009', 'SPDKL_DefaultLocation',
    'SPCONTEXTSTATE', 'SpeechDictationTopicSpelling',
    'SVSFPersistXML', 'SpeechPropertyComplexResponseSpeed',
    'SPWORDLIST', 'SAFTCCITT_ALaw_44kHzStereo',
    'DISPID_SRCreateRecoContext',
    'DISPID_SpeechGrammarRuleStateTransitions',
    'SpeechVoiceCategoryTTSRate', 'DISPID_SpeechLexiconWords',
    'DISPID_SOTId', 'SVSFlagsAsync', 'SAFT22kHz8BitStereo',
    'SPCT_COMMAND', 'SINone', 'DISPID_SLWPronunciations',
    'DISPID_SVEEnginePrivate', 'SPSUnknown', 'DISPID_SPPChildren',
    'SpeechRegistryLocalMachineRoot', 'DISPIDSPTSI_ActiveOffset',
    'ISpNotifySource', 'SPWT_PRONUNCIATION', 'DISPID_SRProfile',
    'DISPID_SRCState', 'SVESentenceBoundary', 'DISPID_SBSRead',
    'DISPID_SFSOpen', 'DISPID_SpeechFileStream',
    'DISPID_SLAddPronunciation', '_ISpeechVoiceEvents',
    'DISPID_SpeechMMSysAudio', 'SPEI_MAX_SR',
    'SPRS_ACTIVE_WITH_AUTO_PAUSE', 'SREAudioLevel',
    'SDA_One_Trailing_Space', 'SPEI_SR_RETAINEDAUDIO',
    'SPRS_INACTIVE', 'eLEXTYPE_PRIVATE18', 'SDA_Two_Trailing_Spaces',
    'DISPID_SPERetainedSizeBytes', 'DISPID_SLWType',
    'SAFTExtendedAudioFormat', 'SDTProperty', 'SPVISEMES',
    'eLEXTYPE_PRIVATE2', 'SAFT8kHz8BitMono', 'SSFMOpenReadWrite',
    'SAFTADPCM_11kHzStereo', 'SpMMAudioEnum', 'SLOStatic',
    'DISPID_SRCVoicePurgeEvent', 'ISpPhrase', 'SVP_14',
    'SAFT44kHz16BitMono', 'SAFTADPCM_22kHzMono',
    'SPWP_UNKNOWN_WORD_UNPRONOUNCEABLE', 'eLEXTYPE_PRIVATE8',
    'DISPID_SPCIdToPhone', 'SRAORetainAudio', 'DISPID_SRCRecognizer',
    'SPAR_Unknown', 'SVEPrivate', 'DISPID_SRCCreateGrammar',
    'SPEI_HYPOTHESIS', 'DISPID_SPAPhraseInfo', 'DISPID_SAEventHandle',
    'SP_VISEME_11', 'SVEVoiceChange', 'DISPID_SGRAttributes',
    'SPEI_RESERVED6', 'DISPID_SpeechMemoryStream',
    'DISPID_SVAllowAudioOuputFormatChangesOnNextSet', 'SVSFNLPMask',
    'SVEAudioLevel', 'ISpEventSource', 'SSSPTRelativeToStart',
    'SECFDefault', 'DISPID_SRGCmdLoadFromObject', 'SPBO_NONE',
    'SPSHORTCUTTYPE', 'SVEAllEvents', 'SPRST_INACTIVE_WITH_PURGE',
    'SpNullPhoneConverter', 'DISPID_SVSRunningState',
    'SpeechPropertyHighConfidenceThreshold',
    'DISPID_SpeechRecoResult', 'SVP_13', 'DISPID_SWFESamplesPerSec',
    'DISPID_SRDisplayUI', 'ISpDataKey', 'SP_VISEME_19',
    'SPPS_RESERVED2', 'STCRemoteServer', 'DISPID_SMSSetData',
    'DISPID_SPRuleConfidence', 'DISPID_SpeechLexiconWord',
    'DISPID_SRSNumberOfActiveRules', 'SVP_11', 'DISPID_SOTs_NewEnum',
    'SPWAVEFORMATTYPE', 'SPCT_DICTATION', 'DISPID_SDKGetBinaryValue',
    'SAFT32kHz8BitStereo', 'SAFTADPCM_11kHzMono',
    'SpObjectTokenCategory', 'SASClosed', 'SASStop',
    'SDTPronunciation', 'eLEXTYPE_RESERVED7', 'STCAll',
    'DISPID_SGRClear', 'SRTExtendableParse',
    'DISPID_SVSyncronousSpeakTimeout', 'DISPID_SLWsItem', 'SVSFIsXML',
    'SREStreamStart', 'ISpeechMMSysAudio', 'SPPS_SuppressWord',
    'SVP_4', 'SDTRule', 'SP_VISEME_2', 'SPCT_SUB_DICTATION',
    'SAFT44kHz16BitStereo', 'ISpeechPhraseInfoBuilder',
    'ISpRecoCategory', 'DISPID_SLPLangId', 'DISPID_SpeechPhraseInfo',
    'DISPID_SpeechObjectTokenCategory', 'DISPID_SBSFormat',
    'DISPID_SPEsCount', 'DISPID_SVSpeak', 'SPEI_VISEME',
    'SpeechAllElements', 'SPXMLRESULTOPTIONS',
    'DISPID_SOTIsUISupported', 'SAFTDefault', 'DISPID_SRRRecoContext',
    'SPAR_High', 'DISPID_SPRNumberOfElements',
    'DISPID_SRCAudioInInterferenceStatus', 'SVF_Stressed',
    'SpPhoneticAlphabetConverter', 'DISPID_SRRSpeakAudio',
    'DISPID_SRGIsPronounceable', 'SRERecognition', 'ISpVoice',
    'DISPID_SRRTimes', 'DISPID_SRGDictationLoad', 'SGLexical',
    'DISPID_SDKEnumKeys', 'SPCT_SLEEP', 'SBOPause', 'SPPS_Noncontent',
    'SPRECOGNIZERSTATUS', 'SPEI_SR_AUDIO_LEVEL', 'SPCATEGORYTYPE',
    'SRSInactiveWithPurge', 'SpeechRecognizerState',
    'DISPID_SASFreeBufferSpace', 'ISpRecognizer', 'SPGRAMMARSTATE'
]

