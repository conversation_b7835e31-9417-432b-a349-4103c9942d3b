# simple_voice_chat.py - <PERSON> Style Simple Voice Chat
import whisper
import speech_recognition as sr
from gtts import gTTS
import pygame
import os
import time
from groq import Groq

class SimpleVoiceChat:
    def __init__(self):
        """Initialize simple voice chat system"""
        print("🚀 Initializing Harry <PERSON>hai Style Voice Chat...")
        
        # Initialize Whisper
        print("📥 Loading Whisper model...")
        self.whisper_model = whisper.load_model("base")
        
        # Initialize Speech Recognition (backup)
        self.recognizer = sr.Recognizer()
        self.microphone = sr.Microphone()
        
        # Initialize Groq
        self.groq_client = Groq(api_key="********************************************************")
        
        # Initialize pygame
        pygame.mixer.init()
        
        print("✅ Voice Chat System Ready!")
    
    def listen_once(self):
        """Listen for one voice input"""
        print("\n🎤 Listening... (Speak now)")
        
        try:
            with self.microphone as source:
                # Adjust for ambient noise
                self.recognizer.adjust_for_ambient_noise(source, duration=1)
                print("🔴 Recording...")
                
                # Listen for audio
                audio = self.recognizer.listen(source, timeout=10, phrase_time_limit=10)
                
                print("🎧 Processing speech...")
                
                # Try Google Speech Recognition first (faster)
                try:
                    text = self.recognizer.recognize_google(audio)
                    print(f"✅ Recognized: {text}")
                    return text
                except:
                    print("🔄 Trying Whisper...")
                    
                    # Save audio for Whisper
                    with open("temp_audio.wav", "wb") as f:
                        f.write(audio.get_wav_data())
                    
                    # Use Whisper
                    result = self.whisper_model.transcribe("temp_audio.wav")
                    text = result["text"].strip()
                    
                    # Clean up
                    if os.path.exists("temp_audio.wav"):
                        os.remove("temp_audio.wav")
                    
                    print(f"✅ Whisper recognized: {text}")
                    return text
                    
        except sr.WaitTimeoutError:
            print("⏰ No speech detected")
            return ""
        except Exception as e:
            print(f"❌ Error: {e}")
            return ""
    
    def generate_doctor_response(self, patient_text):
        """Generate doctor response using Groq"""
        try:
            system_prompt = """You are Dr. Smith, a compassionate and knowledgeable doctor. 
            Respond naturally to patient concerns with empathy and medical expertise. 
            Keep responses conversational, helpful, and under 50 words.
            Ask follow-up questions to understand symptoms better."""
            
            response = self.groq_client.chat.completions.create(
                model="llama3-8b-8192",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"Patient says: {patient_text}"}
                ],
                max_tokens=100,
                temperature=0.7
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            print(f"❌ Groq error: {e}")
            return self.get_fallback_response(patient_text)
    
    def get_fallback_response(self, patient_text):
        """Fallback medical responses"""
        text_lower = patient_text.lower()
        
        if "hello" in text_lower or "hi" in text_lower:
            return "Hello! I'm Dr. Smith. What brings you in today?"
        elif "headache" in text_lower:
            return "I understand you're having headaches. How long have they been bothering you?"
        elif "pain" in text_lower:
            return "Can you describe the pain? Where is it located and how severe?"
        elif "fever" in text_lower:
            return "A fever can indicate infection. Have you taken your temperature?"
        else:
            return "I understand. Can you tell me more about your symptoms?"
    
    def speak_response(self, text):
        """Make doctor speak"""
        print(f"👨‍⚕️ Doctor: {text}")
        print("🔊 Doctor speaking...")
        
        try:
            # Generate speech
            tts = gTTS(text=text, lang='en', slow=False)
            audio_file = "doctor_voice.mp3"
            tts.save(audio_file)
            
            # Play audio
            pygame.mixer.music.load(audio_file)
            pygame.mixer.music.play()
            
            # Wait for playback
            while pygame.mixer.music.get_busy():
                time.sleep(0.1)
            
            # Clean up
            if os.path.exists(audio_file):
                os.remove(audio_file)
                
        except Exception as e:
            print(f"❌ Speech error: {e}")
    
    def start_conversation(self):
        """Start the conversation"""
        print("\n🎯 Harry Bhai Style Voice Conversation Started!")
        print("=" * 50)
        
        # Welcome message
        welcome = "Hello! I'm Dr. Smith. I'm here to help you today. What's bothering you?"
        self.speak_response(welcome)
        
        conversation_history = []
        
        try:
            while True:
                # Listen to patient
                patient_text = self.listen_once()
                
                if not patient_text:
                    continue
                
                # Add to history
                conversation_history.append(f"Patient: {patient_text}")
                print(f"🤒 Patient: {patient_text}")
                
                # Check for exit
                if "bye" in patient_text.lower() or "goodbye" in patient_text.lower():
                    goodbye = "Thank you for coming in today. Take care and feel better soon!"
                    self.speak_response(goodbye)
                    break
                
                # Generate doctor response
                doctor_response = self.generate_doctor_response(patient_text)
                conversation_history.append(f"Doctor: {doctor_response}")
                
                # Doctor speaks
                self.speak_response(doctor_response)
                
                print("\n" + "-" * 30)
                
        except KeyboardInterrupt:
            print("\n🛑 Conversation ended by user")
        
        # Show conversation summary
        print("\n📋 Conversation Summary:")
        print("=" * 30)
        for msg in conversation_history:
            print(msg)
        
        print("\n✅ Conversation completed!")

# Run the chat
if __name__ == "__main__":
    try:
        chat = SimpleVoiceChat()
        chat.start_conversation()
    except Exception as e:
        print(f"❌ System error: {e}")
    
    print("\n🚀 Harry Bhai Style Voice Chat Complete!")
