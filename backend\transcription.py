# backend/transcription.py
import os
from google import genai

# Set your Gemini API key here or use environment variable
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
if not GEMINI_API_KEY:
    raise ValueError("GEMINI_API_KEY environment variable is required!")
client = genai.Client(api_key=GEMINI_API_KEY)

def transcribe_audio(file_path: str) -> str:
    """
    Transcribe audio using Gemini's audio understanding
    """
    myfile = client.files.upload(file=file_path)
    response = client.models.generate_content(
        model="gemini-2.5-flash",
        contents=["Generate a transcript of the speech.", myfile]
    )
    return response.text
