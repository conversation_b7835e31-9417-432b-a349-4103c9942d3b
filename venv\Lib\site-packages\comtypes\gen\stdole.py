from enum import IntFlag

import comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0 as __wrapper_module__
from comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0 import (
    <PERSON><PERSON>_CANCELBOOL, CoClass, OLE_HANDLE, FONTSIZ<PERSON>, OLE_YPOS_PIXELS,
    BSTR, Library, Font, GUID, EXCEPINFO, Gray, Monochrome,
    <PERSON>LE_YSIZE_HIMETRIC, OLE_XPOS_PIXELS, OLE_XSIZE_CONTAINER,
    OLE_XSIZE_PIXELS, OLE_YPOS_HIMETRIC, <PERSON>ON<PERSON><PERSON>LIC, Checked,
    <PERSON><PERSON><PERSON><PERSON><PERSON>, Default, _lcid, O<PERSON>_YSIZE_CONTAINER,
    OLE_ENABLEDEFAULTBOOL, _check_version, StdFont, dispid,
    FONTSTRIKETHROUGH, IDispatch, IPictureDisp, VARIANT_BOOL,
    FONTBOLD, <PERSON><PERSON><PERSON>NDERSCOR<PERSON>, OLE_YSIZE_PIXELS, Unchecked,
    <PERSON><PERSON>_YPOS_CONTAINER, <PERSON>ont, <PERSON>IS<PERSON><PERSON>PER<PERSON>, Picture,
    OLE_XPOS_HIMETRIC, OLE_COLOR, VgaColor, Color, DISPMETHOD,
    IEnumVARIANT, HRESULT, IUnknown, IFontDisp, OLE_XPOS_CONTAINER,
    OLE_OPTEXCLUSIVE, DISPPARAMS, OLE_XSIZE_HIMETRIC, IFontEventsDisp,
    typelib_path, COMMETHOD, IPicture, StdPicture, FontEvents
)


class OLE_TRISTATE(IntFlag):
    Unchecked = 0
    Checked = 1
    Gray = 2


class LoadPictureConstants(IntFlag):
    Default = 0
    Monochrome = 1
    VgaColor = 2
    Color = 4


__all__ = [
    'OLE_CANCELBOOL', 'OLE_YSIZE_PIXELS', 'OLE_HANDLE', 'Unchecked',
    'FONTSIZE', 'OLE_YPOS_CONTAINER', 'IFont', 'OLE_YPOS_PIXELS',
    'Library', 'Font', 'Picture', 'Gray', 'Monochrome',
    'OLE_XPOS_HIMETRIC', 'OLE_YSIZE_HIMETRIC', 'OLE_XPOS_PIXELS',
    'OLE_XSIZE_CONTAINER', 'OLE_XSIZE_PIXELS', 'OLE_YPOS_HIMETRIC',
    'OLE_COLOR', 'FONTITALIC', 'Checked', 'FONTNAME', 'Default',
    'VgaColor', 'Color', 'OLE_YSIZE_CONTAINER',
    'OLE_ENABLEDEFAULTBOOL', 'LoadPictureConstants', 'IFontDisp',
    'OLE_XPOS_CONTAINER', 'OLE_TRISTATE', 'OLE_OPTEXCLUSIVE',
    'OLE_XSIZE_HIMETRIC', 'IFontEventsDisp', 'StdFont',
    'FONTSTRIKETHROUGH', 'typelib_path', 'IPictureDisp', 'IPicture',
    'StdPicture', 'FontEvents', 'FONTBOLD', 'FONTUNDERSCORE'
]

