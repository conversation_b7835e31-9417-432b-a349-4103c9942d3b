# demo_voice_test.py - Quick Test of <PERSON>hai Style Voice System
import whisper
from gtts import gTTS
import pygame
import tempfile
import os
import time

def test_whisper():
    """Test if Whisper is working"""
    print("🚀 Testing Whisper (FREE Speech-to-Text)...")
    try:
        model = whisper.load_model("base")
        print("✅ Whisper model loaded successfully!")
        return model
    except Exception as e:
        print(f"❌ Whisper failed: {e}")
        return None

def test_tts(text="Hello! I'm <PERSON><PERSON>. How can I help you today?"):
    """Test text-to-speech"""
    print("🔊 Testing Text-to-Speech...")
    try:
        pygame.mixer.init()

        tts = gTTS(text=text, lang='en', slow=False)
        temp_file = "temp_doctor_voice.mp3"
        tts.save(temp_file)

        pygame.mixer.music.load(temp_file)
        pygame.mixer.music.play()

        # Wait for playback to finish
        while pygame.mixer.music.get_busy():
            time.sleep(0.1)

        if os.path.exists(temp_file):
            os.unlink(temp_file)

        print("✅ Text-to-Speech working!")
        return True

    except Exception as e:
        print(f"❌ TTS failed: {e}")
        return False

def simulate_conversation():
    """Simulate Harry Bhai style conversation"""
    print("\n🎯 Harry Bhai Style Voice Conversation Demo")
    print("=" * 50)
    
    # Test Whisper
    whisper_model = test_whisper()
    if not whisper_model:
        print("❌ Cannot proceed without Whisper")
        return
    
    # Test TTS
    if not test_tts():
        print("❌ Cannot proceed without TTS")
        return
    
    print("\n🎤 Simulating Real Conversation:")
    print("-" * 30)
    
    # Simulate patient inputs and doctor responses
    conversation = [
        ("Patient", "Hello doctor, I have been having headaches"),
        ("Doctor", "I understand you're having headaches. How long have they been bothering you?"),
        ("Patient", "For about 3 days now, and they're getting worse"),
        ("Doctor", "Can you describe the pain? Is it sharp, dull, or throbbing?"),
        ("Patient", "It's mostly throbbing, especially in the morning"),
        ("Doctor", "That's helpful information. Have you taken any medication for this?")
    ]
    
    for speaker, text in conversation:
        print(f"\n{speaker}: {text}")
        
        if speaker == "Doctor":
            print("🔊 Doctor speaking...")
            test_tts(text)
            time.sleep(1)
        else:
            print("🎤 Patient spoke (would be transcribed by Whisper)")
            time.sleep(2)
    
    print("\n✅ Demo Complete!")
    print("\n🎯 This shows Harry Bhai style conversation:")
    print("   ✅ FREE Whisper for speech-to-text")
    print("   ✅ Natural doctor responses")
    print("   ✅ Real voice synthesis")
    print("   ✅ Continuous conversation flow")

if __name__ == "__main__":
    print("🎤 Harry Bhai Style Voice SOAP Demo")
    print("=" * 40)
    
    try:
        simulate_conversation()
    except KeyboardInterrupt:
        print("\n🛑 Demo stopped by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
    
    print("\n🚀 Ready to integrate with Streamlit!")
